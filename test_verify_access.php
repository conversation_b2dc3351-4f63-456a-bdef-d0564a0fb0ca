<?php
/**
 * Simple test script to check if verify.php is accessible
 */

echo "<h2>Verify.php Access Test</h2>";

// Test 1: Check if verify.php file exists
echo "<h3>1. File Existence Check</h3>";
if (file_exists('verify.php')) {
    echo "✅ verify.php file exists<br>";
    echo "File size: " . filesize('verify.php') . " bytes<br>";
    echo "File permissions: " . substr(sprintf('%o', fileperms('verify.php')), -4) . "<br>";
    echo "Is readable: " . (is_readable('verify.php') ? 'Yes' : 'No') . "<br>";
} else {
    echo "❌ verify.php file not found<br>";
}

// Test 2: Check current directory and URL structure
echo "<h3>2. Directory and URL Structure</h3>";
echo "Current directory: " . getcwd() . "<br>";
echo "Document root: " . $_SERVER['DOCUMENT_ROOT'] . "<br>";
echo "Script name: " . $_SERVER['SCRIPT_NAME'] . "<br>";
echo "Request URI: " . $_SERVER['REQUEST_URI'] . "<br>";

// Test 3: Generate test verification URL
echo "<h3>3. Test Verification URL</h3>";
$test_token = bin2hex(random_bytes(16));
$base_url = "http://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['SCRIPT_NAME']);
$verify_url = $base_url . "/verify.php?token=" . $test_token;

echo "Generated test URL: <a href='$verify_url' target='_blank'>$verify_url</a><br>";
echo "Test token: $test_token<br>";

// Test 4: Check if we can include verify.php (syntax check)
echo "<h3>4. PHP Syntax Check</h3>";
$output = shell_exec('php -l verify.php 2>&1');
if (strpos($output, 'No syntax errors') !== false) {
    echo "✅ verify.php has no syntax errors<br>";
} else {
    echo "❌ verify.php has syntax errors:<br>";
    echo "<pre>" . htmlspecialchars($output) . "</pre>";
}

// Test 5: Check web server configuration
echo "<h3>5. Web Server Information</h3>";
echo "Server software: " . $_SERVER['SERVER_SOFTWARE'] . "<br>";
echo "PHP version: " . phpversion() . "<br>";

// Test 6: Test database connection from verify.php context
echo "<h3>6. Database Connection Test</h3>";
try {
    require_once 'db.php';
    if ($conn && !$conn->connect_error) {
        echo "✅ Database connection successful<br>";
        
        // Test if we can query the parents table
        $result = $conn->query("SELECT COUNT(*) as count FROM parents WHERE verification_token IS NOT NULL");
        if ($result) {
            $row = $result->fetch_assoc();
            echo "✅ Parents table accessible, found " . $row['count'] . " records with tokens<br>";
        } else {
            echo "❌ Cannot query parents table: " . $conn->error . "<br>";
        }
    } else {
        echo "❌ Database connection failed<br>";
    }
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
}

// Test 7: Check .htaccess or URL rewriting
echo "<h3>7. URL Rewriting Check</h3>";
if (file_exists('.htaccess')) {
    echo "✅ .htaccess file exists<br>";
    echo "<details><summary>View .htaccess content</summary>";
    echo "<pre>" . htmlspecialchars(file_get_contents('.htaccess')) . "</pre>";
    echo "</details>";
} else {
    echo "ℹ️ No .htaccess file found<br>";
}

// Test 8: Direct access test
echo "<h3>8. Direct Access Test</h3>";
echo "<p>Try these direct links:</p>";
echo "<ul>";
echo "<li><a href='verify.php' target='_blank'>verify.php (no token)</a> - Should show error message</li>";
echo "<li><a href='verify.php?token=invalid' target='_blank'>verify.php?token=invalid</a> - Should show invalid token error</li>";
echo "<li><a href='verify.php?token=$test_token' target='_blank'>verify.php?token=$test_token</a> - Should show token not found error</li>";
echo "</ul>";

?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3 { color: #333; }
details { margin: 10px 0; }
summary { cursor: pointer; font-weight: bold; }
pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
a { color: #007bff; }
</style>
