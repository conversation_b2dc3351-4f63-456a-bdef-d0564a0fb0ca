<!DOCTYPE html>
<html lang="ms">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Registration - SMKTMI</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 40px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], input[type="email"], input[type="password"], textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-sizing: border-box;
        }
        textarea {
            height: 80px;
            resize: vertical;
        }
        .btn {
            background-color: #4CAF50;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
        }
        .btn:hover {
            background-color: #45a049;
        }
        .btn:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .message {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .password-requirements {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>Test Registration Form - SMKTMI</h2>
        <p>This is a test form to verify the improved registration and email verification system.</p>
        
        <div id="message"></div>
        
        <form id="registrationForm">
            <div class="form-group">
                <label for="full_name">Nama Penuh:</label>
                <input type="text" id="full_name" name="full_name" required>
            </div>
            
            <div class="form-group">
                <label for="email">Alamat Emel:</label>
                <input type="email" id="email" name="email" required>
            </div>
            
            <div class="form-group">
                <label for="phone_number">Nombor Telefon:</label>
                <input type="text" id="phone_number" name="phone_number" required>
            </div>
            
            <div class="form-group">
                <label for="address">Alamat:</label>
                <textarea id="address" name="address" required></textarea>
            </div>
            
            <div class="form-group">
                <label for="password">Kata Laluan:</label>
                <input type="password" id="password" name="password" required>
                <div class="password-requirements">
                    Kata laluan mesti mengandungi sekurang-kurangnya 8 aksara, huruf besar, huruf kecil, nombor dan simbol.
                </div>
            </div>
            
            <div class="form-group">
                <label for="confirm_password">Sahkan Kata Laluan:</label>
                <input type="password" id="confirm_password" name="confirm_password" required>
            </div>
            
            <div class="loading" id="loading">
                <p>Sedang memproses pendaftaran...</p>
            </div>
            
            <button type="submit" class="btn" id="submitBtn">Daftar</button>
        </form>
    </div>

    <script>
        document.getElementById('registrationForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const submitBtn = document.getElementById('submitBtn');
            const loading = document.getElementById('loading');
            const messageDiv = document.getElementById('message');
            
            // Disable submit button and show loading
            submitBtn.disabled = true;
            loading.style.display = 'block';
            messageDiv.innerHTML = '';
            
            // Get form data
            const formData = new FormData(this);
            
            // Send AJAX request
            fetch('register_process.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                loading.style.display = 'none';
                
                if (data.success) {
                    messageDiv.innerHTML = '<div class="message success">' + data.message + '</div>';
                    this.reset(); // Clear form
                } else {
                    messageDiv.innerHTML = '<div class="message error">' + data.message + '</div>';
                }
            })
            .catch(error => {
                loading.style.display = 'none';
                messageDiv.innerHTML = '<div class="message error">Ralat sistem: ' + error.message + '</div>';
                console.error('Error:', error);
            })
            .finally(() => {
                submitBtn.disabled = false;
            });
        });
        
        // Real-time password validation
        document.getElementById('confirm_password').addEventListener('input', function() {
            const password = document.getElementById('password').value;
            const confirmPassword = this.value;
            
            if (confirmPassword && password !== confirmPassword) {
                this.setCustomValidity('Kata laluan tidak sepadan');
            } else {
                this.setCustomValidity('');
            }
        });
        
        document.getElementById('password').addEventListener('input', function() {
            const password = this.value;
            const regex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_]).{8,}$/;
            
            if (password && !regex.test(password)) {
                this.setCustomValidity('Kata laluan tidak memenuhi keperluan');
            } else {
                this.setCustomValidity('');
            }
        });
    </script>
</body>
</html>
