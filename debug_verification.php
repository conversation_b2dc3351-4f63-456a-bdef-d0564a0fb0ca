<?php
/**
 * Debug script to test verification process
 */

require 'db.php';

echo "<h2>Email Verification Debug Tool</h2>";

// Test 1: Check recent registrations
echo "<h3>1. Recent Unverified Registrations</h3>";
$stmt = $conn->prepare("
    SELECT p.parent_id, p.full_name, u.email, p.verification_token, p.token_created_at, p.email_verified 
    FROM parents p 
    JOIN users u ON p.user_id = u.user_id 
    WHERE p.email_verified = 0 AND p.verification_token IS NOT NULL
    ORDER BY p.token_created_at DESC 
    LIMIT 5
");
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Parent ID</th><th>Name</th><th>Email</th><th>Token (first 8 chars)</th><th>Created</th><th>Test Link</th></tr>";
    while ($row = $result->fetch_assoc()) {
        $token_short = substr($row['verification_token'], 0, 8) . '...';
        $test_link = "verify.php?token=" . $row['verification_token'];
        echo "<tr>";
        echo "<td>{$row['parent_id']}</td>";
        echo "<td>{$row['full_name']}</td>";
        echo "<td>{$row['email']}</td>";
        echo "<td>{$token_short}</td>";
        echo "<td>{$row['token_created_at']}</td>";
        echo "<td><a href='$test_link' target='_blank'>Test Verify</a></td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "No unverified registrations found.<br>";
}

// Test 2: Check if verify.php is accessible
echo "<h3>2. File Accessibility Test</h3>";
$verify_file = 'verify.php';
if (file_exists($verify_file)) {
    echo "✅ verify.php exists<br>";
    echo "File size: " . filesize($verify_file) . " bytes<br>";
    echo "Last modified: " . date('Y-m-d H:i:s', filemtime($verify_file)) . "<br>";
    
    // Check if file is readable
    if (is_readable($verify_file)) {
        echo "✅ File is readable<br>";
    } else {
        echo "❌ File is not readable<br>";
    }
} else {
    echo "❌ verify.php does not exist<br>";
}

// Test 3: Test URL construction
echo "<h3>3. URL Construction Test</h3>";
$current_url = "http://" . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
$base_url = dirname($current_url);
$verify_url = $base_url . "/verify.php";

echo "Current URL: $current_url<br>";
echo "Base URL: $base_url<br>";
echo "Verify URL: $verify_url<br>";
echo "Test verify URL: <a href='$verify_url?token=test123' target='_blank'>$verify_url?token=test123</a><br>";

// Test 4: Check server configuration
echo "<h3>4. Server Configuration</h3>";
echo "Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "<br>";
echo "Script Filename: " . $_SERVER['SCRIPT_FILENAME'] . "<br>";
echo "Current Working Directory: " . getcwd() . "<br>";
echo "Server Software: " . $_SERVER['SERVER_SOFTWARE'] . "<br>";

// Test 5: Check for .htaccess rules
echo "<h3>5. .htaccess Check</h3>";
if (file_exists('.htaccess')) {
    echo "✅ .htaccess file exists<br>";
    $htaccess_content = file_get_contents('.htaccess');
    if (strpos($htaccess_content, 'verify') !== false) {
        echo "⚠️ .htaccess contains 'verify' - this might affect routing<br>";
        echo "<details><summary>Show .htaccess content</summary>";
        echo "<pre>" . htmlspecialchars($htaccess_content) . "</pre>";
        echo "</details>";
    } else {
        echo "ℹ️ .htaccess doesn't seem to affect verify.php<br>";
    }
} else {
    echo "ℹ️ No .htaccess file found<br>";
}

// Test 6: Manual token test
echo "<h3>6. Manual Token Test</h3>";
if (isset($_GET['test_token'])) {
    $test_token = $_GET['test_token'];
    echo "Testing token: $test_token<br>";
    
    $stmt = $conn->prepare("SELECT parent_id, token_created_at, email_verified FROM parents WHERE verification_token = ?");
    $stmt->bind_param("s", $test_token);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($row = $result->fetch_assoc()) {
        echo "✅ Token found in database<br>";
        echo "Parent ID: {$row['parent_id']}<br>";
        echo "Created: {$row['token_created_at']}<br>";
        echo "Already verified: " . ($row['email_verified'] ? 'Yes' : 'No') . "<br>";
        
        // Check if token is expired
        $token_created = new DateTime($row['token_created_at']);
        $current_time = new DateTime();
        $diff = $current_time->diff($token_created);
        $hours_old = ($diff->days * 24) + $diff->h;
        
        echo "Token age: $hours_old hours<br>";
        if ($hours_old > 24) {
            echo "⚠️ Token is expired (older than 24 hours)<br>";
        } else {
            echo "✅ Token is still valid<br>";
        }
    } else {
        echo "❌ Token not found in database<br>";
    }
} else {
    echo "Add ?test_token=YOUR_TOKEN to this URL to test a specific token<br>";
}

// Test 7: Create a test registration
echo "<h3>7. Quick Test Registration</h3>";
if (isset($_GET['create_test']) && $_GET['create_test'] === '1') {
    try {
        $test_email = 'test_' . time() . '@example.com';
        $test_token = bin2hex(random_bytes(16));
        $test_time = date('Y-m-d H:i:s');
        
        // Insert test user
        $stmt = $conn->prepare("INSERT INTO users (email, password_hash, role_id) VALUES (?, ?, ?)");
        $password_hash = password_hash('TestPassword123!', PASSWORD_DEFAULT);
        $stmt->bind_param("ssi", $test_email, $password_hash, 2);
        $stmt->execute();
        $user_id = $stmt->insert_id;
        
        // Insert test parent
        $stmt = $conn->prepare("INSERT INTO parents (user_id, full_name, phone_number, address, email, verification_token, token_created_at, email_verified) VALUES (?, ?, ?, ?, ?, ?, ?, 0)");
        $stmt->bind_param("issssss", $user_id, "Test User", "0123456789", "Test Address", $test_email, $test_token, $test_time);
        $stmt->execute();
        
        echo "✅ Test registration created<br>";
        echo "Email: $test_email<br>";
        echo "Token: $test_token<br>";
        echo "Test verification link: <a href='verify.php?token=$test_token' target='_blank'>Click here to test</a><br>";
        
    } catch (Exception $e) {
        echo "❌ Failed to create test registration: " . $e->getMessage() . "<br>";
    }
} else {
    echo "<a href='?create_test=1'>Create Test Registration</a><br>";
}

?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { border-collapse: collapse; width: 100%; margin: 10px 0; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
details { margin: 10px 0; }
summary { cursor: pointer; font-weight: bold; }
pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
h2, h3 { color: #333; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>
