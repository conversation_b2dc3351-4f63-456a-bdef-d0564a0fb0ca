<?php
/**
 * Universal Activity Logger
 * Records all user actions for audit trail (<PERSON><PERSON>, Teacher, Student, Parent)
 */

function logUserActivity($conn, $action, $target_type, $target_id = null, $target_name = null, $description = '', $details = []) {
    // Check if admin_logs table exists
    $table_check = $conn->query("SHOW TABLES LIKE 'admin_logs'");
    if (!$table_check || $table_check->num_rows == 0) {
        return false; // Table doesn't exist
    }

    // Get user information from session
    if (!isset($_SESSION['user_id']) || !isset($_SESSION['role'])) {
        return false;
    }

    $user_id = $_SESSION['user_id'];
    $user_role = $_SESSION['role'];
    $user_name = $_SESSION['name'] ?? null;

    // If name is not in session, get it from database based on role
    if (empty($user_name)) {
        try {
            switch ($user_role) {
                case 'Admin':
                    $stmt = $conn->prepare("SELECT full_name FROM admins WHERE user_id = ?");
                    break;
                case 'Teacher':
                    $stmt = $conn->prepare("SELECT full_name FROM teachers WHERE user_id = ?");
                    break;
                case 'Student':
                    $stmt = $conn->prepare("SELECT full_name FROM students WHERE user_id = ?");
                    break;
                case 'Parent':
                    $stmt = $conn->prepare("SELECT full_name FROM parents WHERE user_id = ?");
                    break;
                default:
                    $user_name = 'Unknown User';
                    break;
            }

            if (isset($stmt)) {
                $stmt->bind_param("i", $user_id);
                $stmt->execute();
                $result = $stmt->get_result();
                if ($row = $result->fetch_assoc()) {
                    $user_name = $row['full_name'];
                }
                $stmt->close();
            }
        } catch (Exception $e) {
            $user_name = 'Unknown User';
        }
    }

    if (empty($user_name)) {
        $user_name = 'Unknown User';
    }

    $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'Unknown';
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown';
    
    // Prepare and execute log insertion
    try {
        $stmt = $conn->prepare("
            INSERT INTO admin_logs (admin_id, admin_name, action, target_type, target_id, target_name, description, ip_address, user_agent)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");

        // Modify description to include user role and details
        $full_description = "[$user_role] $description";

        // Add details if provided
        if (!empty($details) && is_array($details)) {
            $detail_parts = [];
            foreach ($details as $key => $value) {
                if (!empty($value)) {
                    $detail_parts[] = "$key: $value";
                }
            }
            if (!empty($detail_parts)) {
                $full_description .= " | " . implode(", ", $detail_parts);
            }
        }

        $stmt->bind_param("issssisss",
            $user_id,
            $user_name,
            $action,
            $target_type,
            $target_id,
            $target_name,
            $full_description,
            $ip_address,
            $user_agent
        );

        $result = $stmt->execute();

        // Debug: Log any SQL errors
        if (!$result) {
            error_log("Logger SQL Error: " . $stmt->error);
            error_log("Logger Data: user_id=$user_id, user_name=$user_name, action=$action, target_type=$target_type, description=$full_description");
        }

        $stmt->close();

        return $result;
    } catch (Exception $e) {
        error_log("Logger Exception: " . $e->getMessage());
        return false;
    }
}

// Backward compatibility function for admin
function logAdminActivity($conn, $action, $target_type, $target_id = null, $target_name = null, $description = '', $details = []) {
    return logUserActivity($conn, $action, $target_type, $target_id, $target_name, $description, $details);
}

/**
 * Get action color for display
 */
function getActionColor($action) {
    switch (strtoupper($action)) {
        case 'CREATE':
        case 'ADD':
            return '#27ae60'; // Green
        case 'UPDATE':
        case 'EDIT':
            return '#3498db'; // Blue
        case 'DELETE':
        case 'REMOVE':
            return '#e74c3c'; // Red
        case 'LOGIN':
            return '#f39c12'; // Orange
        case 'LOGOUT':
            return '#95a5a6'; // Gray
        case 'VIEW':
            return '#9b59b6'; // Purple
        case 'MARK':
        case 'GRADE':
            return '#16a085'; // Teal
        case 'ATTEND':
        case 'ATTENDANCE':
            return '#8e44ad'; // Purple
        default:
            return '#34495e'; // Dark gray
    }
}

/**
 * Get target type icon
 */
function getTargetTypeIcon($target_type) {
    switch (strtoupper($target_type)) {
        case 'TEACHER':
            return 'fas fa-chalkboard-teacher';
        case 'STUDENT':
            return 'fas fa-user-graduate';
        case 'PARENT':
            return 'fas fa-users';
        case 'CLASSROOM':
        case 'CLASS':
            return 'fas fa-door-open';
        case 'SUBJECT':
            return 'fas fa-book';
        case 'ASSESSMENT':
            return 'fas fa-clipboard-list';
        case 'SCHEDULE':
            return 'fas fa-calendar-alt';
        case 'DISCIPLINE':
            return 'fas fa-exclamation-triangle';
        case 'ATTENDANCE':
            return 'fas fa-calendar-check';
        case 'SYSTEM':
            return 'fas fa-cogs';
        case 'PROFILE':
            return 'fas fa-user-edit';
        default:
            return 'fas fa-file';
    }
}

/**
 * Get user role color
 */
function getUserRoleColor($role) {
    switch (strtoupper($role)) {
        case 'ADMIN':
            return '#e74c3c'; // Red
        case 'TEACHER':
            return '#3498db'; // Blue
        case 'STUDENT':
            return '#27ae60'; // Green
        case 'PARENT':
            return '#f39c12'; // Orange
        default:
            return '#95a5a6'; // Gray
    }
}
?>
