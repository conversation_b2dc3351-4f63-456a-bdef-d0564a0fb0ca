-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Jun 15, 2025 at 07:27 PM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `smktmi-v6`
--

-- --------------------------------------------------------

--
-- Table structure for table `admins`
--

CREATE TABLE `admins` (
  `admin_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `full_name` varchar(100) DEFAULT NULL,
  `phone_number` varchar(20) DEFAULT NULL,
  `last_login` datetime DEFAULT NULL,
  `last_logout` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `admins`
--

INSERT INTO `admins` (`admin_id`, `user_id`, `full_name`, `phone_number`, `last_login`, `last_logout`) VALUES
(1, 1, 'Nurul Nadia', '012-3456789', '2025-06-15 17:58:50', '2025-06-15 18:04:59');

-- --------------------------------------------------------

--
-- Table structure for table `admin_logs`
--

CREATE TABLE `admin_logs` (
  `log_id` int(11) NOT NULL,
  `admin_id` int(11) NOT NULL,
  `admin_name` varchar(255) NOT NULL,
  `action` varchar(100) NOT NULL,
  `target_type` varchar(50) NOT NULL,
  `target_id` int(11) DEFAULT NULL,
  `target_name` varchar(255) DEFAULT NULL,
  `description` text NOT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Admin activity logs table';

--
-- Dumping data for table `admin_logs`
--

INSERT INTO `admin_logs` (`log_id`, `admin_id`, `admin_name`, `action`, `target_type`, `target_id`, `target_name`, `description`, `ip_address`, `user_agent`, `created_at`) VALUES
(1, 1, 'Nurul Nadia', 'CREATE', 'SYSTEM', NULL, NULL, 'Admin logs table created', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-09 18:22:12'),
(2, 1, 'Nurul Nadia', 'CREATE', 'CLASSROOM', 28, '0', 'Kelas baru dicipta: Tingkatan 1 Test (Tingkatan 1)', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-09 18:23:01'),
(3, 1, 'Nurul Nadia', 'CREATE', 'CLASSROOM', 29, '0', '[Admin] Kelas baru dicipta: Tingkatan 2 Cubaan (Tingkatan 2)', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-09 18:32:54'),
(4, 1, 'Nurul Nadia', 'DELETE', 'CLASSROOM', 29, '0', '[Admin] Kelas dipadam: Tingkatan 2 Cubaan (Tingkatan 2)', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-09 18:32:59'),
(5, 2, 'Cikgu Aminah Binti Abdul', 'UPDATE', 'PROFILE', 1, '0', '[Teacher] Maklumat guru dikemaskini', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-09 18:41:01'),
(6, 1, 'Nurul Nadia', 'TEST', 'SYSTEM', NULL, '0', '[Admin] Testing logging functionality', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-09 18:47:52'),
(7, 2, 'Cikgu Aminah Binti Abdul', 'UPDATE', 'PROFILE', 1, '0', '[Teacher] Maklumat guru dikemaskini', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-09 18:48:10'),
(8, 2, 'Cikgu Aminah Binti Abdul', 'UPDATE', 'PROFILE', 1, '0', '[Teacher] Maklumat guru dikemaskini', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-09 18:48:12'),
(9, 2, 'Cikgu Aminah Binti Abdul', 'UPDATE', 'PROFILE', 1, '0', '[Teacher] Maklumat guru dikemaskini', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-09 18:51:15'),
(10, 2, 'Cikgu Aminah Binti Abdul', 'UPDATE', 'PROFILE', 1, '0', '[Teacher] Maklumat guru dikemaskini', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-09 19:00:26'),
(11, 2, 'Cikgu Aminah Binti Abdul', 'UPDATE', 'PROFILE', 1, '0', '[Teacher] Maklumat guru dikemaskini', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-09 19:00:46'),
(12, 2, 'Cikgu Aminah Binti Abdul', 'UPDATE', 'PROFILE', 1, '0', '[Teacher] Maklumat guru dikemaskini', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-09 19:01:12'),
(13, 2, 'Cikgu Aminah Binti Abdul', 'UPDATE', 'PROFILE', 1, '0', '[Teacher] Maklumat guru dikemaskini | Nama Lama: Cikgu Aminah Binti Abdul, Nama Baru: Cikgu Aminah Binti Abdul, Telefon Lama: 0197545688, Telefon Baru: 0197545688', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-09 19:05:05'),
(14, 2, 'Cikgu Aminah Binti Abdul', 'UPDATE', 'PROFILE', 1, '0', '[Teacher] Maklumat guru dikemaskini | Nama Lama: Cikgu Aminah Binti Abdul, Nama Baru: Cikgu Aminah Binti Abdul, Telefon Lama: 0197545688, Telefon Baru: 0197545680', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-09 19:05:09'),
(15, 126, 'Firkhan Bin Zulkifli', 'UPDATE', 'PROFILE', 126, '0', '[Student] Kata laluan dikemaskini | Jenis Perubahan: Kata Laluan, Status: Berjaya, Keselamatan: Memenuhi keperluan keselamatan', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-09 19:08:50'),
(16, 39, 'Fatimah Binti Zahar', 'UPDATE', 'PROFILE', 23, '0', '[Parent] Maklumat ibu bapa dikemaskini | Nama Lama: Fatimah Binti Zahar, Nama Baru: Fatimah Binti Zahar, Telefon Lama: 0161234567, Telefon Baru: 0161234567, Alamat Lama: 303 Jalan Satria, Batu Pahat, Alamat Baru: 303 Jalan Kemboja, Batu Pahat', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-09 19:13:07'),
(17, 2, 'Cikgu Aminah Binti Abdul', 'MARK', 'ASSESSMENT', 21, '0', '[Teacher] Markah dimasukkan untuk penilaian | Jenis Penilaian: Ujian 3, Subjek: Perakaunan, Kelas: Tingkatan 5 Hambali, Pelajar Berjaya: 2', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-09 19:14:22'),
(18, 1, 'Nurul Nadia', 'UPDATE', 'CLASSROOM', 15, '0', '[Admin] Kelas dikemaskini: Tingkatan 1 Ghazalii (Tingkatan 1)', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-09 19:59:26'),
(19, 1, 'Nurul Nadia', 'UPDATE', 'CLASSROOM', 15, '0', '[Admin] Kelas dikemaskini: Tingkatan 1 Ghazali (Tingkatan 1)', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-09 19:59:34'),
(20, 1, 'Nurul Nadia', 'UPDATE', 'TEACHER', 8, '0', '[Admin] Maklumat guru dikemaskini: Cikgu Ali Bin Mansor (ID: TCH008)', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-10 03:15:55'),
(21, 1, 'Nurul Nadia', 'DELETE', 'CLASSROOM', 19, '0', '[Admin] Kelas dipadam | Nama Kelas: Tingkatan 3 Hambali, Tingkatan: 3, Sebab Padam: Tiada pelajar, guru atau jadual yang berkaitan', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-10 03:18:00'),
(22, 1, 'Nurul Nadia', 'CREATE', 'CLASSROOM', 30, '0', '[Admin] Kelas baru dicipta: Tingkatan 3 Hambali (Tingkatan 3)', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-10 03:18:58'),
(23, 2, 'Cikgu Aminah Binti Abdul', 'ATTENDANCE', 'CLASSROOM', 5, '0', '[Teacher] Kehadiran direkodkan untuk kelas Tingkatan 5 Hanafi | Kelas: Tingkatan 5 Hanafi, Tarikh: 2025-06-10, Jumlah Pelajar: 7, Hadir: 6, Tidak Hadir: 1', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-10 03:36:24'),
(24, 2, 'Cikgu Aminah Binti Abdul', 'MARK', 'ASSESSMENT', 19, '0', '[Teacher] Markah dimasukkan untuk penilaian | Jenis Penilaian: Ujian 3, Subjek: Bahasa Melayu, Kelas: Tingkatan 5 Hambali, Pelajar Berjaya: 2', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-10 03:39:45'),
(25, 2, 'Cikgu Aminah Binti Abdul', 'CREATE', 'DISCIPLINE', 5, '0', '[Teacher] Laporan disiplin dibuat | Pelajar: Hafiz Bin Jamaludin, Kelas: Tingkatan 5 Hanafi, Jenis Kesalahan: Merokok, Tahap Keseriusan: Berat, Tarikh Kejadian: 2025-06-10, Tindakan Diambil: jsdkcfhisd', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-10 03:43:42'),
(26, 2, 'Cikgu Aminah Binti Abdul', 'CREATE', 'DISCIPLINE', 6, '0', '[Teacher] Laporan disiplin dibuat | Pelajar: Faris Bin Zainal, Kelas: Tingkatan 5 Hanafi, Jenis Kesalahan: Ponteng Kelas, Tahap Keseriusan: Sederhana, Tarikh Kejadian: 2025-06-10, Tindakan Diambil: we           w', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-10 03:44:24'),
(27, 9, 'Cikgu Ali Bin Mansor', 'CREATE', 'DISCIPLINE', 7, '0', '[Teacher] Laporan disiplin dibuat | Pelajar: Salmiah Binti Zulkifli, Kelas: Tingkatan 5 Syafie, Jenis Kesalahan: Membawa Telefon Bimbit Tanpa Kebenaran, Tahap Keseriusan: Sederhana, Tarikh Kejadian: 2025-06-10, Tindakan Diambil: potpetpotpet', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-10 12:15:44'),
(28, 2, 'Cikgu Aminah Binti Abdul', 'ATTENDANCE', 'CLASSROOM', 5, '0', '[Teacher] Kehadiran direkodkan untuk kelas Tingkatan 5 Hanafi | Kelas: Tingkatan 5 Hanafi, Tarikh: 2025-06-14, Jumlah Pelajar: 7, Hadir: 4, Tidak Hadir: 3', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-14 16:57:31'),
(29, 2, 'Cikgu Aminah Binti Abdul', 'UPDATE', 'PROFILE', 1, '0', '[Teacher] Maklumat guru dikemaskini | Nama Lama: Cikgu Aminah Binti Abdul, Nama Baru: Cikgu Aminah Binti Abdullah, Telefon Lama: 0197545680, Telefon Baru: 0197545680', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-14 16:58:00'),
(30, 2, 'Cikgu Aminah Binti Abdullah', 'MARK', 'ASSESSMENT', 18, '0', '[Teacher] Markah dimasukkan untuk penilaian | Jenis Penilaian: Ujian 2, Peperiksaan Pertengahan Tahun, Subjek: Perakaunan, Kelas: Tingkatan 5 Hambali, Pelajar Berjaya: 2', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-14 16:59:16'),
(31, 1, 'Nurul Nadia', 'UPDATE', 'TEACHER', 15, '0', '[Admin] Maklumat guru dikemaskini: Cikgu Danish Bin Mazlan (ID: TCH012)', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-15 04:59:08'),
(32, 1, 'Nurul Nadia', 'UPDATE', 'TEACHER', 11, '0', '[Admin] Maklumat guru dikemaskini: Cikgu Sulaiman Bin Ahmad (ID: TCH013)', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-15 05:00:00'),
(33, 1, 'Nurul Nadia', 'UPDATE', 'TEACHER', 12, '0', '[Admin] Maklumat guru dikemaskini: Cikgu Suriani Binti Ishak (ID: TCH014)', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-15 05:00:17'),
(34, 1, 'Nurul Nadia', 'UPDATE', 'TEACHER', 13, '0', '[Admin] Maklumat guru dikemaskini: Cikgu Jauhari Bin Ahmad Nizam (ID: TCH002)', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-15 05:00:47'),
(35, 2, 'Cikgu Aminah Binti Abdullah', 'MARK', 'ASSESSMENT', 23, '0', '[Teacher] Markah dimasukkan untuk penilaian | Jenis Penilaian: Ujian 3, Subjek: Bahasa Melayu, Kelas: Tingkatan 5 Hanafi, Pelajar Berjaya: 8', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-15 05:33:40'),
(36, 1, 'Nurul Nadia', 'CREATE', 'TEACHER', 16, '0', '[Admin] Guru baru didaftarkan | Nama Penuh: Dalila binti Rizwan, ID Staf: TCH016, Email: <EMAIL>, Jantina: Perempuan, Nombor Telefon: 0196754238', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-15 06:35:42'),
(37, 1, 'Nurul Nadia', 'CREATE', 'TEACHER', 17, '0', '[Admin] Guru baru didaftarkan | Nama Penuh: Salwa binti Aidil, ID Staf: TCH017, Email: <EMAIL>, Jantina: Perempuan, Nombor Telefon: 0167823899', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-15 06:36:21'),
(38, 1, 'Nurul Nadia', 'CREATE', 'TEACHER', 18, '0', '[Admin] Guru baru didaftarkan | Nama Penuh: Nasrul bin Faiz, ID Staf: TCH018, Email: <EMAIL>, Jantina: Lelaki, Nombor Telefon: 0196574300', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-15 06:37:18'),
(39, 1, 'Nurul Nadia', 'CREATE', 'TEACHER', 19, '0', '[Admin] Guru baru didaftarkan | Nama Penuh: Badrul bin Isa, ID Staf: TCH019, Email: <EMAIL>, Jantina: Lelaki, Nombor Telefon: 0127685677', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-15 06:39:11'),
(40, 1, 'Nurul Nadia', 'UPDATE', 'TEACHER', 19, '0', '[Admin] Maklumat guru dikemaskini: Cikgu Badrul bin Isa (ID: TCH019)', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-15 06:39:29'),
(41, 2, 'Cikgu Aminah Binti Abdullah', 'ATTENDANCE', 'CLASSROOM', 5, '0', '[Teacher] Kehadiran direkodkan untuk kelas Tingkatan 5 Hanafi | Kelas: Tingkatan 5 Hanafi, Tarikh: 2025-06-15, Jumlah Pelajar: 10, Hadir: 6, Tidak Hadir: 4', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-15 06:56:45'),
(42, 2, 'Cikgu Aminah Binti Abdullah', 'ATTENDANCE', 'CLASSROOM', 5, '0', '[Teacher] Kehadiran direkodkan untuk kelas Tingkatan 5 Hanafi | Kelas: Tingkatan 5 Hanafi, Tarikh: 2025-06-15, Jumlah Pelajar: 10, Hadir: 7, Tidak Hadir: 3', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-15 13:51:23'),
(43, 2, 'Cikgu Aminah Binti Abdullah', 'MARK', 'ASSESSMENT', 23, '0', '[Teacher] Markah dimasukkan untuk penilaian | Jenis Penilaian: Ujian 3, Subjek: Bahasa Melayu, Kelas: Tingkatan 5 Hanafi, Pelajar Berjaya: 10', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-15 14:12:38'),
(44, 2, 'Cikgu Aminah Binti Abdullah', 'MARK', 'ASSESSMENT', 23, '0', '[Teacher] Markah dimasukkan untuk penilaian | Jenis Penilaian: Ujian 3, Subjek: Bahasa Melayu, Kelas: Tingkatan 5 Hanafi, Pelajar Berjaya: 10', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-15 14:12:44'),
(45, 2, 'Cikgu Aminah Binti Abdullah', 'MARK', 'ASSESSMENT', 23, '0', '[Teacher] Markah dimasukkan untuk penilaian | Jenis Penilaian: Ujian 3, Subjek: Bahasa Melayu, Kelas: Tingkatan 5 Hanafi, Pelajar Berjaya: 10', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-15 14:12:47'),
(46, 2, 'Cikgu Aminah Binti Abdullah', 'MARK', 'ASSESSMENT', 23, '0', '[Teacher] Markah dimasukkan untuk penilaian | Jenis Penilaian: Ujian 3, Subjek: Bahasa Melayu, Kelas: Tingkatan 5 Hanafi, Pelajar Berjaya: 10', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-15 14:12:52'),
(47, 2, 'Cikgu Aminah Binti Abdullah', 'MARK', 'ASSESSMENT', 23, '0', '[Teacher] Markah dimasukkan untuk penilaian | Jenis Penilaian: Ujian 3, Subjek: Bahasa Melayu, Kelas: Tingkatan 5 Hanafi, Pelajar Berjaya: 10', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-15 14:12:58'),
(48, 2, 'Cikgu Aminah Binti Abdullah', 'MARK', 'ASSESSMENT', 23, '0', '[Teacher] Markah dimasukkan untuk penilaian | Jenis Penilaian: Ujian 3, Subjek: Bahasa Melayu, Kelas: Tingkatan 5 Hanafi, Pelajar Berjaya: 10', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-15 14:13:02'),
(49, 2, 'Cikgu Aminah Binti Abdullah', 'MARK', 'ASSESSMENT', 23, '0', '[Teacher] Markah dimasukkan untuk penilaian | Jenis Penilaian: Ujian 3, Subjek: Bahasa Melayu, Kelas: Tingkatan 5 Hanafi, Pelajar Berjaya: 10', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-15 14:13:13'),
(50, 173, 'Cikgu Amira Binti Kamal', 'MARK', 'ASSESSMENT', 22, '0', '[Teacher] Markah dimasukkan untuk penilaian | Jenis Penilaian: Ujian 3, Subjek: Bahasa Inggeris, Kelas: Tingkatan 5 Hanafi, Pelajar Berjaya: 10', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-15 14:52:23'),
(51, 6, 'Cikgu Nurul Huda Binti Razak', 'MARK', 'ASSESSMENT', 24, '0', '[Teacher] Markah dimasukkan untuk penilaian | Jenis Penilaian: Ujian 3, Subjek: Pendidikan Islam, Kelas: Tingkatan 5 Hanafi, Pelajar Berjaya: 10', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-15 14:53:04'),
(52, 6, 'Cikgu Nurul Huda Binti Razak', 'MARK', 'ASSESSMENT', 24, '0', '[Teacher] Markah dimasukkan untuk penilaian | Jenis Penilaian: Ujian 3, Subjek: Pendidikan Islam, Kelas: Tingkatan 5 Hanafi, Pelajar Berjaya: 10', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-15 14:53:09'),
(53, 1, 'Nurul Nadia', 'CREATE', 'TEACHER', 20, '0', '[Admin] Guru baru didaftarkan | Nama Penuh: Arliyana binti Mokhtar, ID Staf: TCH020, Email: <EMAIL>, Jantina: Perempuan, Nombor Telefon: 0197865456', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-15 15:59:41'),
(54, 1, 'Nurul Nadia', 'UPDATE', 'TEACHER', 20, '0', '[Admin] Maklumat guru dikemaskini: Cikgu Arliyana binti Mokhtar (ID: TCH020)', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-15 16:00:01'),
(55, 2, 'Cikgu Aminah Binti Abdullah', 'ATTENDANCE', 'CLASSROOM', 5, '0', '[Teacher] Kehadiran direkodkan untuk kelas Tingkatan 5 Hanafi | Kelas: Tingkatan 5 Hanafi, Tarikh: 2025-06-15, Jumlah Pelajar: 11, Hadir: 8, Tidak Hadir: 3', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-15 16:08:28'),
(56, 2, 'Cikgu Aminah Binti Abdullah', 'MARK', 'ASSESSMENT', 31, '0', '[Teacher] Markah dimasukkan untuk penilaian | Jenis Penilaian: Ujian Akhir Tahun, Subjek: Bahasa Melayu, Kelas: Tingkatan 5 Hanafi, Pelajar Berjaya: 11', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-15 16:09:56'),
(57, 2, 'Cikgu Aminah Binti Abdullah', 'CREATE', 'DISCIPLINE', 8, '0', '[Teacher] Laporan disiplin dibuat | Pelajar: Amin Irfan Bin Nazri, Kelas: Tingkatan 4 Ghazali, Jenis Kesalahan: Tidak Memakai Pakaian Seragam Lengkap, Tahap Keseriusan: Ringan, Tarikh Kejadian: 2025-06-15, Tindakan Diambil: Surat amaran', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-15 16:11:31');

-- --------------------------------------------------------

--
-- Table structure for table `assessment`
--

CREATE TABLE `assessment` (
  `assessment_id` int(11) NOT NULL,
  `assessment_type` varchar(100) NOT NULL,
  `assessment_date` date NOT NULL,
  `subject_id` int(11) NOT NULL,
  `classroom_id` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Assessment/Exam information table';

--
-- Dumping data for table `assessment`
--

INSERT INTO `assessment` (`assessment_id`, `assessment_type`, `assessment_date`, `subject_id`, `classroom_id`, `created_at`, `updated_at`) VALUES
(22, 'Ujian 3', '2025-06-07', 2, 5, '2025-06-02 14:17:35', '2025-06-02 14:17:35'),
(23, 'Ujian 3', '2025-06-07', 1, 5, '2025-06-02 14:17:35', '2025-06-02 14:17:35'),
(24, 'Ujian 3', '2025-06-07', 6, 5, '2025-06-02 14:17:35', '2025-06-02 14:17:35'),
(28, 'Peperiksaan Pertengahan Tahun', '2025-06-07', 1, 3, '2025-06-15 05:03:08', '2025-06-15 05:03:08'),
(29, 'Peperiksaan Pertengahan Tahun', '2025-06-07', 12, 3, '2025-06-15 05:03:08', '2025-06-15 05:03:08'),
(30, 'Ujian Akhir Tahun', '2025-07-12', 2, 5, '2025-06-15 05:39:47', '2025-06-15 05:39:47'),
(31, 'Ujian Akhir Tahun', '2025-07-12', 1, 5, '2025-06-15 05:39:47', '2025-06-15 05:39:47'),
(32, 'Ujian Akhir Tahun', '2025-07-12', 6, 5, '2025-06-15 05:39:47', '2025-06-15 05:39:47'),
(36, 'Ujian Pertengahan Tahun', '2025-06-12', 2, 5, '2025-06-15 05:42:32', '2025-06-15 05:42:32'),
(37, 'Ujian Pertengahan Tahun', '2025-06-12', 1, 5, '2025-06-15 05:42:32', '2025-06-15 05:42:32'),
(38, 'Ujian Pertengahan Tahun', '2025-06-12', 6, 5, '2025-06-15 05:42:32', '2025-06-15 05:42:32'),
(39, 'Ujian 1', '2025-07-20', 12, 3, '2025-06-15 06:42:42', '2025-06-15 06:42:42'),
(40, 'Ujian 1', '2025-07-20', 14, 3, '2025-06-15 06:42:42', '2025-06-15 06:42:42'),
(41, 'Peperiksaan Akhir Tahun', '2025-07-13', 2, 5, '2025-06-15 16:02:28', '2025-06-15 16:02:28'),
(42, 'Peperiksaan Akhir Tahun', '2025-07-13', 1, 5, '2025-06-15 16:02:28', '2025-06-15 16:02:28'),
(43, 'Peperiksaan Akhir Tahun', '2025-07-13', 6, 5, '2025-06-15 16:02:28', '2025-06-15 16:02:28');

-- --------------------------------------------------------

--
-- Table structure for table `assessment_result`
--

CREATE TABLE `assessment_result` (
  `result_id` int(11) NOT NULL,
  `student_id` int(11) NOT NULL,
  `assessment_id` int(11) NOT NULL,
  `marks` decimal(5,2) NOT NULL DEFAULT 0.00,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Student assessment results table';

--
-- Dumping data for table `assessment_result`
--

INSERT INTO `assessment_result` (`result_id`, `student_id`, `assessment_id`, `marks`, `created_at`, `updated_at`) VALUES
(1, 46, 2, 98.00, '2025-05-31 13:56:34', '2025-05-31 13:56:34'),
(2, 41, 2, 90.00, '2025-05-31 13:56:34', '2025-05-31 13:56:34'),
(3, 35, 2, 88.00, '2025-05-31 13:56:34', '2025-05-31 13:56:34'),
(4, 47, 2, 78.00, '2025-05-31 13:56:34', '2025-05-31 13:56:34'),
(5, 45, 2, 89.00, '2025-05-31 13:56:34', '2025-05-31 13:56:34'),
(6, 37, 2, 40.00, '2025-05-31 13:56:34', '2025-05-31 13:56:34'),
(7, 39, 2, 56.00, '2025-05-31 13:56:34', '2025-05-31 13:56:34'),
(8, 46, 3, 0.00, '2025-05-31 16:39:56', '2025-05-31 16:39:56'),
(9, 41, 3, 0.00, '2025-05-31 16:39:56', '2025-05-31 16:39:56'),
(10, 35, 3, 56.00, '2025-05-31 16:39:56', '2025-05-31 16:39:56'),
(11, 47, 3, 0.00, '2025-05-31 16:39:56', '2025-05-31 16:39:56'),
(12, 45, 3, 0.00, '2025-05-31 16:39:56', '2025-05-31 16:39:56'),
(13, 37, 3, 0.00, '2025-05-31 16:39:56', '2025-05-31 16:39:56'),
(14, 39, 3, 0.00, '2025-05-31 16:39:56', '2025-05-31 16:39:56'),
(15, 32, 2, 89.00, '2025-05-31 18:02:47', '2025-05-31 18:02:47'),
(16, 34, 2, 90.00, '2025-05-31 18:02:47', '2025-05-31 18:02:47'),
(40, 46, 23, 89.00, '2025-06-02 14:18:24', '2025-06-15 14:12:44'),
(41, 41, 23, 68.00, '2025-06-02 14:18:24', '2025-06-15 14:12:58'),
(42, 35, 23, 57.00, '2025-06-02 14:18:24', '2025-06-15 14:13:02'),
(43, 47, 23, 99.00, '2025-06-02 14:18:24', '2025-06-02 14:18:24'),
(44, 45, 23, 78.00, '2025-06-02 14:18:24', '2025-06-02 14:18:24'),
(45, 37, 23, 80.00, '2025-06-02 14:18:24', '2025-06-02 14:18:24'),
(46, 39, 23, 60.00, '2025-06-02 14:18:24', '2025-06-15 14:13:13'),
(49, 32, 18, 65.00, '2025-06-14 16:59:16', '2025-06-14 16:59:16'),
(50, 34, 18, 76.00, '2025-06-14 16:59:16', '2025-06-14 16:59:16'),
(51, 49, 23, 89.00, '2025-06-15 05:33:40', '2025-06-15 05:33:40'),
(52, 56, 23, 87.00, '2025-06-15 14:12:38', '2025-06-15 14:12:47'),
(53, 57, 23, 78.00, '2025-06-15 14:12:38', '2025-06-15 14:12:52'),
(54, 46, 22, 0.00, '2025-06-15 14:52:23', '2025-06-15 14:52:23'),
(55, 56, 22, 0.00, '2025-06-15 14:52:23', '2025-06-15 14:52:23'),
(56, 57, 22, 0.00, '2025-06-15 14:52:23', '2025-06-15 14:52:23'),
(57, 41, 22, 85.00, '2025-06-15 14:52:23', '2025-06-15 14:52:23'),
(58, 35, 22, 0.00, '2025-06-15 14:52:23', '2025-06-15 14:52:23'),
(59, 47, 22, 0.00, '2025-06-15 14:52:23', '2025-06-15 14:52:23'),
(60, 49, 22, 0.00, '2025-06-15 14:52:23', '2025-06-15 14:52:23'),
(61, 45, 22, 0.00, '2025-06-15 14:52:23', '2025-06-15 14:52:23'),
(62, 37, 22, 0.00, '2025-06-15 14:52:23', '2025-06-15 14:52:23'),
(63, 39, 22, 0.00, '2025-06-15 14:52:23', '2025-06-15 14:52:23'),
(64, 46, 24, 0.00, '2025-06-15 14:53:04', '2025-06-15 14:53:04'),
(65, 56, 24, 0.00, '2025-06-15 14:53:04', '2025-06-15 14:53:04'),
(66, 57, 24, 0.00, '2025-06-15 14:53:04', '2025-06-15 14:53:04'),
(67, 41, 24, 93.00, '2025-06-15 14:53:04', '2025-06-15 14:53:09'),
(68, 35, 24, 0.00, '2025-06-15 14:53:04', '2025-06-15 14:53:04'),
(69, 47, 24, 0.00, '2025-06-15 14:53:04', '2025-06-15 14:53:04'),
(70, 49, 24, 0.00, '2025-06-15 14:53:04', '2025-06-15 14:53:04'),
(71, 45, 24, 0.00, '2025-06-15 14:53:04', '2025-06-15 14:53:04'),
(72, 37, 24, 0.00, '2025-06-15 14:53:04', '2025-06-15 14:53:04'),
(73, 39, 24, 0.00, '2025-06-15 14:53:04', '2025-06-15 14:53:04'),
(74, 46, 31, 56.00, '2025-06-15 16:09:56', '2025-06-15 16:09:56'),
(75, 56, 31, 78.00, '2025-06-15 16:09:56', '2025-06-15 16:09:56'),
(76, 57, 31, 90.00, '2025-06-15 16:09:56', '2025-06-15 16:09:56'),
(77, 41, 31, 0.00, '2025-06-15 16:09:56', '2025-06-15 16:09:56'),
(78, 35, 31, 0.00, '2025-06-15 16:09:56', '2025-06-15 16:09:56'),
(79, 54, 31, 0.00, '2025-06-15 16:09:56', '2025-06-15 16:09:56'),
(80, 47, 31, 0.00, '2025-06-15 16:09:56', '2025-06-15 16:09:56'),
(81, 49, 31, 0.00, '2025-06-15 16:09:56', '2025-06-15 16:09:56'),
(82, 45, 31, 0.00, '2025-06-15 16:09:56', '2025-06-15 16:09:56'),
(83, 37, 31, 0.00, '2025-06-15 16:09:56', '2025-06-15 16:09:56'),
(84, 39, 31, 0.00, '2025-06-15 16:09:56', '2025-06-15 16:09:56');

-- --------------------------------------------------------

--
-- Table structure for table `attendance`
--

CREATE TABLE `attendance` (
  `attendance_id` int(11) NOT NULL,
  `student_id` int(11) DEFAULT NULL,
  `teacher_id` int(11) DEFAULT NULL,
  `classroom_id` int(11) DEFAULT NULL,
  `attendance_date` date DEFAULT NULL,
  `status` enum('Hadir','Tidak Hadir') DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `attendance`
--

INSERT INTO `attendance` (`attendance_id`, `student_id`, `teacher_id`, `classroom_id`, `attendance_date`, `status`) VALUES
(1991, 35, 1, 5, '2025-01-01', 'Hadir'),
(1992, 37, 1, 5, '2025-01-01', 'Hadir'),
(1993, 39, 1, 5, '2025-01-01', 'Hadir'),
(1994, 41, 1, 5, '2025-01-01', 'Hadir'),
(1995, 45, 1, 5, '2025-01-01', 'Hadir'),
(1996, 46, 1, 5, '2025-01-01', 'Hadir'),
(1997, 47, 1, 5, '2025-01-01', 'Hadir'),
(1998, 35, 1, 5, '2025-01-02', 'Hadir'),
(1999, 37, 1, 5, '2025-01-02', 'Hadir'),
(2000, 39, 1, 5, '2025-01-02', 'Hadir'),
(2001, 41, 1, 5, '2025-01-02', 'Hadir'),
(2002, 45, 1, 5, '2025-01-02', 'Hadir'),
(2003, 46, 1, 5, '2025-01-02', 'Hadir'),
(2004, 47, 1, 5, '2025-01-02', 'Hadir'),
(2005, 35, 1, 5, '2025-01-03', NULL),
(2006, 37, 1, 5, '2025-01-03', NULL),
(2007, 39, 1, 5, '2025-01-03', NULL),
(2008, 41, 1, 5, '2025-01-03', 'Hadir'),
(2009, 45, 1, 5, '2025-01-03', NULL),
(2010, 46, 1, 5, '2025-01-03', NULL),
(2011, 47, 1, 5, '2025-01-03', NULL),
(2012, 35, 1, 5, '2025-01-04', NULL),
(2013, 37, 1, 5, '2025-01-04', NULL),
(2014, 39, 1, 5, '2025-01-04', NULL),
(2015, 41, 1, 5, '2025-01-04', 'Hadir'),
(2016, 45, 1, 5, '2025-01-04', NULL),
(2017, 46, 1, 5, '2025-01-04', NULL),
(2018, 47, 1, 5, '2025-01-04', NULL),
(2019, 35, 1, 5, '2025-01-05', 'Hadir'),
(2020, 37, 1, 5, '2025-01-05', 'Hadir'),
(2021, 39, 1, 5, '2025-01-05', 'Hadir'),
(2022, 41, 1, 5, '2025-01-05', 'Hadir'),
(2023, 45, 1, 5, '2025-01-05', 'Hadir'),
(2024, 46, 1, 5, '2025-01-05', 'Hadir'),
(2025, 47, 1, 5, '2025-01-05', 'Hadir'),
(2026, 35, 1, 5, '2025-01-06', NULL),
(2027, 37, 1, 5, '2025-01-06', NULL),
(2028, 39, 1, 5, '2025-01-06', NULL),
(2029, 41, 1, 5, '2025-01-06', NULL),
(2030, 45, 1, 5, '2025-01-06', NULL),
(2031, 46, 1, 5, '2025-01-06', NULL),
(2032, 47, 1, 5, '2025-01-06', NULL),
(2033, 35, 1, 5, '2025-01-07', 'Hadir'),
(2034, 37, 1, 5, '2025-01-07', NULL),
(2035, 39, 1, 5, '2025-01-07', NULL),
(2036, 41, 1, 5, '2025-01-07', NULL),
(2037, 45, 1, 5, '2025-01-07', NULL),
(2038, 46, 1, 5, '2025-01-07', NULL),
(2039, 47, 1, 5, '2025-01-07', 'Hadir'),
(2040, 35, 1, 5, '2025-01-08', 'Hadir'),
(2041, 37, 1, 5, '2025-01-08', 'Hadir'),
(2042, 39, 1, 5, '2025-01-08', 'Hadir'),
(2043, 41, 1, 5, '2025-01-08', 'Hadir'),
(2044, 45, 1, 5, '2025-01-08', 'Hadir'),
(2045, 46, 1, 5, '2025-01-08', 'Hadir'),
(2046, 47, 1, 5, '2025-01-08', 'Hadir'),
(2047, 35, 1, 5, '2025-01-09', 'Hadir'),
(2048, 37, 1, 5, '2025-01-09', 'Hadir'),
(2049, 39, 1, 5, '2025-01-09', 'Hadir'),
(2050, 41, 1, 5, '2025-01-09', 'Hadir'),
(2051, 45, 1, 5, '2025-01-09', 'Hadir'),
(2052, 46, 1, 5, '2025-01-09', 'Hadir'),
(2053, 47, 1, 5, '2025-01-09', 'Hadir'),
(2054, 35, 1, 5, '2025-01-10', 'Hadir'),
(2055, 37, 1, 5, '2025-01-10', NULL),
(2056, 39, 1, 5, '2025-01-10', NULL),
(2057, 41, 1, 5, '2025-01-10', NULL),
(2058, 45, 1, 5, '2025-01-10', NULL),
(2059, 46, 1, 5, '2025-01-10', NULL),
(2060, 47, 1, 5, '2025-01-10', 'Hadir'),
(2061, 35, 1, 5, '2025-01-11', NULL),
(2062, 37, 1, 5, '2025-01-11', NULL),
(2063, 39, 1, 5, '2025-01-11', NULL),
(2064, 41, 1, 5, '2025-01-11', NULL),
(2065, 45, 1, 5, '2025-01-11', NULL),
(2066, 46, 1, 5, '2025-01-11', NULL),
(2067, 47, 1, 5, '2025-01-11', NULL),
(2068, 35, 1, 5, '2025-01-12', NULL),
(2069, 37, 1, 5, '2025-01-12', NULL),
(2070, 39, 1, 5, '2025-01-12', NULL),
(2071, 41, 1, 5, '2025-01-12', NULL),
(2072, 45, 1, 5, '2025-01-12', NULL),
(2073, 46, 1, 5, '2025-01-12', NULL),
(2074, 47, 1, 5, '2025-01-12', NULL),
(2075, 35, 1, 5, '2025-01-14', 'Hadir'),
(2076, 37, 1, 5, '2025-01-14', 'Hadir'),
(2077, 39, 1, 5, '2025-01-14', 'Hadir'),
(2078, 41, 1, 5, '2025-01-14', 'Hadir'),
(2079, 45, 1, 5, '2025-01-14', 'Hadir'),
(2080, 46, 1, 5, '2025-01-14', 'Hadir'),
(2081, 47, 1, 5, '2025-01-14', 'Hadir'),
(2082, 35, 1, 5, '2025-01-15', NULL),
(2083, 37, 1, 5, '2025-01-15', 'Hadir'),
(2084, 39, 1, 5, '2025-01-15', NULL),
(2085, 41, 1, 5, '2025-01-15', NULL),
(2086, 45, 1, 5, '2025-01-15', NULL),
(2087, 46, 1, 5, '2025-01-15', NULL),
(2088, 47, 1, 5, '2025-01-15', NULL),
(2089, 35, 1, 5, '2025-01-16', NULL),
(2090, 37, 1, 5, '2025-01-16', NULL),
(2091, 39, 1, 5, '2025-01-16', 'Hadir'),
(2092, 41, 1, 5, '2025-01-16', NULL),
(2093, 45, 1, 5, '2025-01-16', NULL),
(2094, 46, 1, 5, '2025-01-16', NULL),
(2095, 47, 1, 5, '2025-01-16', NULL),
(2096, 35, 1, 5, '2025-01-17', NULL),
(2097, 37, 1, 5, '2025-01-17', NULL),
(2098, 39, 1, 5, '2025-01-17', NULL),
(2099, 41, 1, 5, '2025-01-17', 'Hadir'),
(2100, 45, 1, 5, '2025-01-17', NULL),
(2101, 46, 1, 5, '2025-01-17', NULL),
(2102, 47, 1, 5, '2025-01-17', NULL),
(2103, 35, 1, 5, '2025-01-18', 'Hadir'),
(2104, 37, 1, 5, '2025-01-18', 'Hadir'),
(2105, 39, 1, 5, '2025-01-18', 'Hadir'),
(2106, 41, 1, 5, '2025-01-18', 'Hadir'),
(2107, 45, 1, 5, '2025-01-18', 'Hadir'),
(2108, 46, 1, 5, '2025-01-18', 'Hadir'),
(2109, 47, 1, 5, '2025-01-18', 'Hadir'),
(2110, 35, 1, 5, '2025-01-19', NULL),
(2111, 37, 1, 5, '2025-01-19', NULL),
(2112, 39, 1, 5, '2025-01-19', NULL),
(2113, 41, 1, 5, '2025-01-19', NULL),
(2114, 45, 1, 5, '2025-01-19', NULL),
(2115, 46, 1, 5, '2025-01-19', NULL),
(2116, 47, 1, 5, '2025-01-19', NULL),
(2145, 35, 1, 5, '2025-01-24', NULL),
(2146, 37, 1, 5, '2025-01-24', NULL),
(2147, 39, 1, 5, '2025-01-24', NULL),
(2148, 41, 1, 5, '2025-01-24', NULL),
(2149, 45, 1, 5, '2025-01-24', NULL),
(2150, 46, 1, 5, '2025-01-24', NULL),
(2151, 47, 1, 5, '2025-01-24', NULL),
(2152, 35, 1, 5, '2025-01-20', NULL),
(2153, 37, 1, 5, '2025-01-20', NULL),
(2154, 39, 1, 5, '2025-01-20', 'Hadir'),
(2155, 41, 1, 5, '2025-01-20', 'Hadir'),
(2156, 45, 1, 5, '2025-01-20', 'Hadir'),
(2157, 46, 1, 5, '2025-01-20', 'Hadir'),
(2158, 47, 1, 5, '2025-01-20', 'Hadir'),
(2159, 35, 1, 5, '2025-01-21', NULL),
(2160, 37, 1, 5, '2025-01-21', 'Hadir'),
(2161, 39, 1, 5, '2025-01-21', NULL),
(2162, 41, 1, 5, '2025-01-21', 'Hadir'),
(2163, 45, 1, 5, '2025-01-21', NULL),
(2164, 46, 1, 5, '2025-01-21', NULL),
(2165, 47, 1, 5, '2025-01-21', NULL),
(2166, 35, 1, 5, '2025-01-22', 'Hadir'),
(2167, 37, 1, 5, '2025-01-22', NULL),
(2168, 39, 1, 5, '2025-01-22', NULL),
(2169, 41, 1, 5, '2025-01-22', 'Hadir'),
(2170, 45, 1, 5, '2025-01-22', NULL),
(2171, 46, 1, 5, '2025-01-22', NULL),
(2172, 47, 1, 5, '2025-01-22', NULL),
(2173, 35, 1, 5, '2025-01-23', NULL),
(2174, 37, 1, 5, '2025-01-23', 'Hadir'),
(2175, 39, 1, 5, '2025-01-23', NULL),
(2176, 41, 1, 5, '2025-01-23', 'Hadir'),
(2177, 45, 1, 5, '2025-01-23', NULL),
(2178, 46, 1, 5, '2025-01-23', NULL),
(2179, 47, 1, 5, '2025-01-23', NULL),
(2180, 35, 1, 5, '2025-01-24', NULL),
(2181, 37, 1, 5, '2025-01-24', NULL),
(2182, 39, 1, 5, '2025-01-24', 'Hadir'),
(2183, 41, 1, 5, '2025-01-24', 'Hadir'),
(2184, 45, 1, 5, '2025-01-24', 'Hadir'),
(2185, 46, 1, 5, '2025-01-24', 'Hadir'),
(2186, 47, 1, 5, '2025-01-24', 'Hadir'),
(2201, 35, 1, 5, '2025-01-25', NULL),
(2202, 37, 1, 5, '2025-01-25', NULL),
(2203, 39, 1, 5, '2025-01-25', NULL),
(2204, 41, 1, 5, '2025-01-25', NULL),
(2205, 45, 1, 5, '2025-01-25', NULL),
(2206, 46, 1, 5, '2025-01-25', NULL),
(2207, 47, 1, 5, '2025-01-25', NULL),
(2208, 35, 1, 5, '2025-01-26', 'Hadir'),
(2209, 37, 1, 5, '2025-01-26', 'Hadir'),
(2210, 39, 1, 5, '2025-01-26', 'Hadir'),
(2211, 41, 1, 5, '2025-01-26', 'Hadir'),
(2212, 45, 1, 5, '2025-01-26', 'Hadir'),
(2213, 46, 1, 5, '2025-01-26', 'Hadir'),
(2214, 47, 1, 5, '2025-01-26', 'Hadir'),
(2215, 35, 1, 5, '2025-01-27', 'Hadir'),
(2216, 37, 1, 5, '2025-01-27', 'Hadir'),
(2217, 39, 1, 5, '2025-01-27', 'Hadir'),
(2218, 41, 1, 5, '2025-01-27', 'Hadir'),
(2219, 45, 1, 5, '2025-01-27', 'Hadir'),
(2220, 46, 1, 5, '2025-01-27', 'Hadir'),
(2221, 47, 1, 5, '2025-01-27', 'Hadir'),
(2222, 35, 1, 5, '2025-01-28', 'Hadir'),
(2223, 37, 1, 5, '2025-01-28', NULL),
(2224, 39, 1, 5, '2025-01-28', NULL),
(2225, 41, 1, 5, '2025-01-28', NULL),
(2226, 45, 1, 5, '2025-01-28', NULL),
(2227, 46, 1, 5, '2025-01-28', NULL),
(2228, 47, 1, 5, '2025-01-28', 'Hadir'),
(2229, 35, 1, 5, '2025-01-29', 'Hadir'),
(2230, 37, 1, 5, '2025-01-29', NULL),
(2231, 39, 1, 5, '2025-01-29', NULL),
(2232, 41, 1, 5, '2025-01-29', NULL),
(2233, 45, 1, 5, '2025-01-29', NULL),
(2234, 46, 1, 5, '2025-01-29', NULL),
(2235, 47, 1, 5, '2025-01-29', 'Hadir'),
(2236, 35, 1, 5, '2025-01-30', NULL),
(2237, 37, 1, 5, '2025-01-30', 'Hadir'),
(2238, 39, 1, 5, '2025-01-30', 'Hadir'),
(2239, 41, 1, 5, '2025-01-30', 'Hadir'),
(2240, 45, 1, 5, '2025-01-30', 'Hadir'),
(2241, 46, 1, 5, '2025-01-30', 'Hadir'),
(2242, 47, 1, 5, '2025-01-30', NULL),
(2243, 35, 1, 5, '2025-01-31', NULL),
(2244, 37, 1, 5, '2025-01-31', NULL),
(2245, 39, 1, 5, '2025-01-31', 'Hadir'),
(2246, 41, 1, 5, '2025-01-31', 'Hadir'),
(2247, 45, 1, 5, '2025-01-31', 'Hadir'),
(2248, 46, 1, 5, '2025-01-31', NULL),
(2249, 47, 1, 5, '2025-01-31', NULL),
(2250, 35, 1, 5, '2025-01-31', NULL),
(2251, 37, 1, 5, '2025-01-31', NULL),
(2252, 39, 1, 5, '2025-01-31', 'Hadir'),
(2253, 41, 1, 5, '2025-01-31', 'Hadir'),
(2254, 45, 1, 5, '2025-01-31', 'Hadir'),
(2255, 46, 1, 5, '2025-01-31', NULL),
(2256, 47, 1, 5, '2025-01-31', NULL),
(2775, 35, 1, 5, '2025-07-01', 'Hadir'),
(2776, 37, 1, 5, '2025-07-01', 'Hadir'),
(2777, 39, 1, 5, '2025-07-01', 'Hadir'),
(2778, 41, 1, 5, '2025-07-01', 'Hadir'),
(2779, 45, 1, 5, '2025-07-01', 'Hadir'),
(2780, 46, 1, 5, '2025-07-01', 'Hadir'),
(2781, 47, 1, 5, '2025-07-01', 'Hadir'),
(2782, 35, 1, 5, '2025-07-02', 'Hadir'),
(2783, 37, 1, 5, '2025-07-02', 'Hadir'),
(2784, 39, 1, 5, '2025-07-02', 'Hadir'),
(2785, 41, 1, 5, '2025-07-02', 'Hadir'),
(2786, 45, 1, 5, '2025-07-02', 'Hadir'),
(2787, 46, 1, 5, '2025-07-02', 'Hadir'),
(2788, 47, 1, 5, '2025-07-02', 'Hadir'),
(2789, 35, 1, 5, '2025-07-03', NULL),
(2790, 37, 1, 5, '2025-07-03', NULL),
(2791, 39, 1, 5, '2025-07-03', NULL),
(2792, 41, 1, 5, '2025-07-03', 'Hadir'),
(2793, 45, 1, 5, '2025-07-03', NULL),
(2794, 46, 1, 5, '2025-07-03', NULL),
(2795, 47, 1, 5, '2025-07-03', NULL),
(2796, 35, 1, 5, '2025-07-04', NULL),
(2797, 37, 1, 5, '2025-07-04', NULL),
(2798, 39, 1, 5, '2025-07-04', NULL),
(2799, 41, 1, 5, '2025-07-04', 'Hadir'),
(2800, 45, 1, 5, '2025-07-04', NULL),
(2801, 46, 1, 5, '2025-07-04', NULL),
(2802, 47, 1, 5, '2025-07-04', NULL),
(2803, 35, 1, 5, '2025-07-05', 'Hadir'),
(2804, 37, 1, 5, '2025-07-05', 'Hadir'),
(2805, 39, 1, 5, '2025-07-05', 'Hadir'),
(2806, 41, 1, 5, '2025-07-05', 'Hadir'),
(2807, 45, 1, 5, '2025-07-05', 'Hadir'),
(2808, 46, 1, 5, '2025-07-05', 'Hadir'),
(2809, 47, 1, 5, '2025-07-05', 'Hadir'),
(2810, 35, 1, 5, '2025-07-06', 'Hadir'),
(2811, 37, 1, 5, '2025-07-06', 'Hadir'),
(2812, 39, 1, 5, '2025-07-06', 'Hadir'),
(2813, 41, 1, 5, '2025-07-06', 'Hadir'),
(2814, 45, 1, 5, '2025-07-06', 'Hadir'),
(2815, 46, 1, 5, '2025-07-06', 'Hadir'),
(2816, 47, 1, 5, '2025-07-06', 'Hadir'),
(2817, 35, 1, 5, '2025-07-07', NULL),
(2818, 37, 1, 5, '2025-07-07', NULL),
(2819, 39, 1, 5, '2025-07-07', NULL),
(2820, 41, 1, 5, '2025-07-07', NULL),
(2821, 45, 1, 5, '2025-07-07', NULL),
(3013, 35, 1, 5, '2025-07-15', 'Tidak Hadir'),
(3014, 37, 1, 5, '2025-07-15', 'Tidak Hadir'),
(3015, 39, 1, 5, '2025-07-15', NULL),
(3016, 41, 1, 5, '2025-07-15', NULL),
(3017, 45, 1, 5, '2025-07-15', NULL),
(3018, 46, 1, 5, '2025-07-15', NULL),
(3019, 47, 1, 5, '2025-07-15', NULL),
(3020, 35, 1, 5, '2025-07-16', 'Hadir'),
(3021, 37, 1, 5, '2025-07-16', 'Hadir'),
(3022, 39, 1, 5, '2025-07-16', 'Hadir'),
(3023, 41, 1, 5, '2025-07-16', 'Hadir'),
(3024, 45, 1, 5, '2025-07-16', 'Hadir'),
(3025, 46, 1, 5, '2025-07-16', 'Hadir'),
(3026, 47, 1, 5, '2025-07-16', 'Hadir'),
(3027, 35, 1, 5, '2025-07-17', 'Hadir'),
(3028, 37, 1, 5, '2025-07-17', NULL),
(3029, 39, 1, 5, '2025-07-17', NULL),
(3030, 41, 1, 5, '2025-07-17', NULL),
(3031, 45, 1, 5, '2025-07-17', NULL),
(3032, 46, 1, 5, '2025-07-17', NULL),
(3033, 47, 1, 5, '2025-07-17', 'Hadir'),
(3034, 35, 1, 5, '2025-07-18', 'Hadir'),
(3035, 37, 1, 5, '2025-07-18', NULL),
(3036, 39, 1, 5, '2025-07-18', NULL),
(3037, 41, 1, 5, '2025-07-18', NULL),
(3038, 45, 1, 5, '2025-07-18', NULL),
(3039, 46, 1, 5, '2025-07-18', NULL),
(3040, 47, 1, 5, '2025-07-18', 'Hadir'),
(3041, 35, 1, 5, '2025-07-19', NULL),
(3042, 37, 1, 5, '2025-07-19', 'Hadir'),
(3043, 39, 1, 5, '2025-07-19', NULL),
(3044, 41, 1, 5, '2025-07-19', NULL),
(3045, 45, 1, 5, '2025-07-19', NULL),
(3046, 46, 1, 5, '2025-07-19', 'Hadir'),
(3047, 47, 1, 5, '2025-07-19', NULL),
(3048, 35, 1, 5, '2025-07-20', NULL),
(3049, 37, 1, 5, '2025-07-20', NULL),
(3050, 39, 1, 5, '2025-07-20', 'Hadir'),
(3051, 41, 1, 5, '2025-07-20', 'Hadir'),
(3052, 45, 1, 5, '2025-07-20', 'Hadir'),
(3053, 46, 1, 5, '2025-07-20', NULL),
(3054, 47, 1, 5, '2025-07-20', NULL),
(3055, 35, 1, 5, '2025-07-21', NULL),
(3056, 37, 1, 5, '2025-07-21', NULL),
(3057, 39, 1, 5, '2025-07-21', NULL),
(3058, 41, 1, 5, '2025-07-21', NULL),
(3059, 45, 1, 5, '2025-07-21', NULL),
(3060, 46, 1, 5, '2025-07-21', NULL),
(3061, 47, 1, 5, '2025-07-21', NULL),
(3090, 35, 1, 5, '2025-07-22', NULL),
(3091, 37, 1, 5, '2025-07-22', NULL),
(3092, 39, 1, 5, '2025-07-22', 'Hadir'),
(3093, 41, 1, 5, '2025-07-22', 'Hadir'),
(3094, 45, 1, 5, '2025-07-22', 'Hadir'),
(3095, 46, 1, 5, '2025-07-22', 'Hadir'),
(3096, 47, 1, 5, '2025-07-22', 'Hadir'),
(3097, 35, 1, 5, '2025-07-23', NULL),
(3098, 37, 1, 5, '2025-07-23', 'Hadir'),
(3099, 39, 1, 5, '2025-07-23', NULL),
(3100, 41, 1, 5, '2025-07-23', 'Hadir'),
(3101, 45, 1, 5, '2025-07-23', NULL),
(3102, 46, 1, 5, '2025-07-23', NULL),
(3103, 47, 1, 5, '2025-07-23', NULL),
(3104, 35, 1, 5, '2025-07-24', 'Hadir'),
(3105, 37, 1, 5, '2025-07-24', NULL),
(3106, 39, 1, 5, '2025-07-24', NULL),
(3107, 41, 1, 5, '2025-07-24', 'Hadir'),
(3108, 45, 1, 5, '2025-07-24', NULL),
(3109, 46, 1, 5, '2025-07-24', NULL),
(3110, 47, 1, 5, '2025-07-24', NULL),
(3111, 35, 1, 5, '2025-07-25', NULL),
(3112, 37, 1, 5, '2025-07-25', 'Hadir'),
(3113, 39, 1, 5, '2025-07-25', NULL),
(3114, 41, 1, 5, '2025-07-25', 'Hadir'),
(3115, 45, 1, 5, '2025-07-25', NULL),
(3116, 46, 1, 5, '2025-07-25', NULL),
(3117, 47, 1, 5, '2025-07-25', NULL),
(3118, 35, 1, 5, '2025-07-26', NULL),
(3119, 37, 1, 5, '2025-07-26', NULL),
(3120, 39, 1, 5, '2025-07-26', 'Hadir'),
(3121, 41, 1, 5, '2025-07-26', 'Hadir'),
(3122, 45, 1, 5, '2025-07-26', 'Hadir'),
(3123, 46, 1, 5, '2025-07-26', 'Hadir'),
(3124, 47, 1, 5, '2025-07-26', 'Hadir'),
(3125, 35, 1, 5, '2025-07-27', 'Hadir'),
(3126, 37, 1, 5, '2025-07-27', NULL),
(3127, 39, 1, 5, '2025-07-27', NULL),
(3128, 41, 1, 5, '2025-07-27', NULL),
(3129, 45, 1, 5, '2025-07-27', NULL),
(3130, 46, 1, 5, '2025-07-27', NULL),
(3131, 47, 1, 5, '2025-07-27', NULL),
(3132, 35, 1, 5, '2025-07-28', NULL),
(3133, 37, 1, 5, '2025-07-28', 'Hadir'),
(3134, 39, 1, 5, '2025-07-28', NULL),
(3135, 41, 1, 5, '2025-07-28', NULL),
(3136, 45, 1, 5, '2025-07-28', NULL),
(3137, 46, 1, 5, '2025-07-28', NULL),
(3138, 47, 1, 5, '2025-07-28', NULL),
(3139, 35, 1, 5, '2025-07-29', NULL),
(3140, 37, 1, 5, '2025-07-29', NULL),
(3141, 39, 1, 5, '2025-07-29', 'Hadir'),
(3142, 41, 1, 5, '2025-07-29', 'Hadir'),
(3143, 45, 1, 5, '2025-07-29', 'Hadir'),
(3144, 46, 1, 5, '2025-07-29', 'Hadir'),
(3145, 47, 1, 5, '2025-07-29', 'Hadir'),
(3146, 35, 1, 5, '2025-07-30', NULL),
(3147, 37, 1, 5, '2025-07-30', 'Hadir'),
(3148, 39, 1, 5, '2025-07-30', NULL),
(3149, 41, 1, 5, '2025-07-30', NULL),
(3150, 45, 1, 5, '2025-07-30', NULL),
(3151, 46, 1, 5, '2025-07-30', NULL),
(3152, 47, 1, 5, '2025-07-30', NULL),
(3153, 35, 1, 5, '2025-07-31', 'Hadir'),
(3154, 37, 1, 5, '2025-07-31', NULL),
(3155, 39, 1, 5, '2025-07-31', NULL),
(3156, 41, 1, 5, '2025-07-31', NULL),
(3157, 45, 1, 5, '2025-07-31', NULL),
(3158, 46, 1, 5, '2025-07-31', NULL),
(3159, 47, 1, 5, '2025-07-31', NULL),
(3160, 35, 1, 5, '2025-07-26', NULL),
(3161, 37, 1, 5, '2025-07-26', NULL),
(3162, 39, 1, 5, '2025-07-26', NULL),
(3163, 41, 1, 5, '2025-07-26', NULL),
(3164, 45, 1, 5, '2025-07-26', NULL),
(3165, 46, 1, 5, '2025-07-26', NULL),
(3166, 47, 1, 5, '2025-07-26', NULL),
(3167, 35, 1, 5, '2025-08-01', 'Tidak Hadir'),
(3168, 37, 1, 5, '2025-08-01', 'Tidak Hadir'),
(3169, 39, 1, 5, '2025-08-01', 'Tidak Hadir'),
(3170, 41, 1, 5, '2025-08-01', 'Tidak Hadir'),
(3171, 45, 1, 5, '2025-08-01', 'Tidak Hadir'),
(3172, 46, 1, 5, '2025-08-01', 'Tidak Hadir'),
(3173, 47, 1, 5, '2025-08-01', 'Tidak Hadir'),
(3174, 35, 1, 5, '2025-08-02', 'Tidak Hadir'),
(3175, 37, 1, 5, '2025-08-02', 'Tidak Hadir'),
(3176, 39, 1, 5, '2025-08-02', 'Tidak Hadir'),
(3177, 41, 1, 5, '2025-08-02', 'Tidak Hadir'),
(3178, 45, 1, 5, '2025-08-02', 'Tidak Hadir'),
(3179, 46, 1, 5, '2025-08-02', 'Tidak Hadir'),
(3180, 47, 1, 5, '2025-08-02', 'Tidak Hadir'),
(3181, 35, 1, 5, '2025-08-03', 'Tidak Hadir'),
(3182, 37, 1, 5, '2025-08-03', 'Tidak Hadir'),
(3183, 39, 1, 5, '2025-08-03', 'Tidak Hadir'),
(3184, 41, 1, 5, '2025-08-03', 'Tidak Hadir'),
(3185, 45, 1, 5, '2025-08-03', 'Tidak Hadir'),
(3186, 46, 1, 5, '2025-08-03', 'Tidak Hadir'),
(3187, 47, 1, 5, '2025-08-03', 'Tidak Hadir'),
(3202, 35, 1, 5, '2025-07-14', NULL),
(3203, 37, 1, 5, '2025-07-14', NULL),
(3204, 39, 1, 5, '2025-07-14', NULL),
(3205, 41, 1, 5, '2025-07-14', NULL),
(3206, 45, 1, 5, '2025-07-14', NULL),
(3207, 46, 1, 5, '2025-07-14', NULL),
(3208, 47, 1, 5, '2025-07-14', NULL),
(3349, 35, 1, 5, '2025-07-10', 'Hadir'),
(3350, 35, 1, 5, '2025-07-11', 'Hadir'),
(3351, 35, 1, 5, '2025-07-12', 'Hadir'),
(3352, 35, 1, 5, '2025-07-13', 'Hadir'),
(3353, 35, 1, 5, '2025-07-14', NULL),
(3354, 37, 1, 5, '2025-07-10', 'Hadir'),
(3355, 37, 1, 5, '2025-07-11', NULL),
(3356, 37, 1, 5, '2025-07-12', NULL),
(3357, 37, 1, 5, '2025-07-13', 'Hadir'),
(3358, 37, 1, 5, '2025-07-14', 'Hadir'),
(3359, 39, 1, 5, '2025-07-10', 'Hadir'),
(3360, 39, 1, 5, '2025-07-11', NULL),
(3361, 39, 1, 5, '2025-07-12', NULL),
(3362, 39, 1, 5, '2025-07-13', 'Hadir'),
(3363, 39, 1, 5, '2025-07-14', 'Hadir'),
(3364, 41, 1, 5, '2025-07-10', 'Hadir'),
(3365, 41, 1, 5, '2025-07-11', 'Hadir'),
(3366, 41, 1, 5, '2025-07-12', 'Hadir'),
(3367, 41, 1, 5, '2025-07-13', 'Hadir'),
(3368, 41, 1, 5, '2025-07-14', NULL),
(3369, 45, 1, 5, '2025-07-10', 'Hadir'),
(3370, 45, 1, 5, '2025-07-11', NULL),
(3371, 45, 1, 5, '2025-07-12', NULL),
(3372, 45, 1, 5, '2025-07-13', 'Hadir'),
(3373, 45, 1, 5, '2025-07-14', 'Hadir'),
(3374, 46, 1, 5, '2025-07-10', 'Hadir'),
(3375, 46, 1, 5, '2025-07-11', NULL),
(3376, 46, 1, 5, '2025-07-12', NULL),
(3377, 46, 1, 5, '2025-07-13', 'Hadir'),
(3378, 46, 1, 5, '2025-07-14', 'Hadir'),
(3379, 47, 1, 5, '2025-07-10', 'Hadir'),
(3380, 47, 1, 5, '2025-07-11', 'Hadir'),
(3381, 47, 1, 5, '2025-07-12', 'Hadir'),
(3382, 47, 1, 5, '2025-07-13', 'Hadir'),
(3383, 47, 1, 5, '2025-07-14', NULL),
(3384, 46, 1, 5, '2025-07-07', NULL),
(3385, 47, 1, 5, '2025-07-07', NULL),
(3386, 46, 1, 5, '2025-07-09', NULL),
(3387, 47, 1, 5, '2025-07-09', NULL),
(3388, 46, 1, 5, '2025-07-07', NULL),
(3389, 46, 1, 5, '2025-07-09', NULL),
(3390, 47, 1, 5, '2025-07-09', NULL),
(3391, 35, 1, 5, '2025-07-14', 'Tidak Hadir'),
(3392, 35, 1, 5, '2025-07-15', 'Tidak Hadir'),
(3393, 37, 1, 5, '2025-07-15', 'Tidak Hadir'),
(3394, 37, 1, 5, '2025-07-17', 'Hadir'),
(3395, 39, 1, 5, '2025-07-17', 'Hadir'),
(3396, 41, 1, 5, '2025-07-17', 'Hadir'),
(3397, 45, 1, 5, '2025-07-17', 'Hadir'),
(3398, 46, 1, 5, '2025-07-17', 'Hadir'),
(3399, 35, 1, 5, '2025-07-09', 'Hadir'),
(3400, 37, 1, 5, '2025-07-09', 'Hadir'),
(3401, 39, 1, 5, '2025-07-09', 'Hadir'),
(3402, 41, 1, 5, '2025-07-09', 'Hadir'),
(3403, 45, 1, 5, '2025-07-09', 'Hadir'),
(3404, 46, 1, 5, '2025-07-09', 'Hadir'),
(3405, 47, 1, 5, '2025-07-09', 'Hadir'),
(3406, 39, 1, 5, '2025-07-26', 'Hadir'),
(3407, 41, 1, 5, '2025-07-26', 'Hadir'),
(3408, 45, 1, 5, '2025-07-26', 'Hadir'),
(3409, 46, 1, 5, '2025-07-26', 'Hadir'),
(3410, 47, 1, 5, '2025-07-26', 'Hadir'),
(3411, 47, 1, 5, '2025-07-07', 'Tidak Hadir'),
(3412, 35, 1, 5, '2025-01-12', NULL),
(3413, 37, 1, 5, '2025-01-12', NULL),
(3414, 39, 1, 5, '2025-01-12', NULL),
(3415, 41, 1, 5, '2025-01-12', NULL),
(3416, 45, 1, 5, '2025-01-12', NULL),
(3417, 46, 1, 5, '2025-01-12', NULL),
(3418, 47, 1, 5, '2025-01-12', NULL),
(3419, 35, 1, 5, '2025-01-12', NULL),
(3420, 37, 1, 5, '2025-01-12', NULL),
(3421, 39, 1, 5, '2025-01-12', NULL),
(3422, 41, 1, 5, '2025-01-12', NULL),
(3423, 45, 1, 5, '2025-01-12', NULL),
(3424, 46, 1, 5, '2025-01-12', NULL),
(3425, 47, 1, 5, '2025-01-12', NULL),
(3426, 35, 1, 5, '2025-02-04', NULL),
(3427, 37, 1, 5, '2025-02-04', NULL),
(3428, 37, 1, 5, '2025-02-02', 'Hadir'),
(3429, 37, 1, 5, '2025-02-06', 'Hadir'),
(3430, 41, 1, 5, '2025-02-02', NULL),
(3431, 41, 1, 5, '2025-02-05', NULL),
(3432, 35, 1, 5, '2025-02-01', 'Hadir'),
(3433, 37, 1, 5, '2025-02-01', 'Hadir'),
(3434, 39, 1, 5, '2025-02-01', 'Hadir'),
(3435, 41, 1, 5, '2025-02-01', 'Hadir'),
(3436, 45, 1, 5, '2025-02-01', 'Hadir'),
(3437, 46, 1, 5, '2025-02-01', 'Hadir'),
(3438, 47, 1, 5, '2025-02-01', 'Hadir'),
(3439, 39, 1, 5, '2025-02-02', NULL),
(3440, 41, 1, 5, '2025-02-02', NULL),
(3441, 35, 1, 5, '2025-02-03', NULL),
(3442, 37, 1, 5, '2025-02-03', NULL),
(3443, 39, 1, 5, '2025-02-03', 'Hadir'),
(3444, 41, 1, 5, '2025-02-03', NULL),
(3445, 45, 1, 5, '2025-02-03', NULL),
(3446, 46, 1, 5, '2025-02-03', NULL),
(3447, 47, 1, 5, '2025-02-03', NULL),
(3448, 39, 1, 5, '2025-02-04', NULL),
(3449, 41, 1, 5, '2025-02-04', 'Hadir'),
(3450, 39, 1, 5, '2025-02-05', 'Hadir'),
(3451, 41, 1, 5, '2025-02-05', NULL),
(3452, 35, 1, 5, '2025-02-06', NULL),
(3453, 37, 1, 5, '2025-02-06', 'Hadir'),
(3454, 39, 1, 5, '2025-02-06', NULL),
(3455, 41, 1, 5, '2025-02-06', NULL),
(3456, 45, 1, 5, '2025-02-06', NULL),
(3457, 46, 1, 5, '2025-02-06', NULL),
(3458, 47, 1, 5, '2025-02-06', NULL),
(3459, 39, 1, 5, '2025-02-07', 'Hadir'),
(3460, 41, 1, 5, '2025-02-07', 'Hadir'),
(3461, 35, 1, 5, '2025-02-08', NULL),
(3462, 37, 1, 5, '2025-02-08', NULL),
(3463, 39, 1, 5, '2025-02-08', NULL),
(3464, 41, 1, 5, '2025-02-08', NULL),
(3465, 45, 1, 5, '2025-02-08', NULL),
(3466, 46, 1, 5, '2025-02-08', NULL),
(3467, 47, 1, 5, '2025-02-08', NULL),
(3468, 35, 1, 5, '2025-02-04', NULL),
(3469, 37, 1, 5, '2025-02-04', NULL),
(3470, 37, 1, 5, '2025-02-02', 'Hadir'),
(3471, 37, 1, 5, '2025-02-06', 'Hadir'),
(3472, 41, 1, 5, '2025-02-02', NULL),
(3473, 41, 1, 5, '2025-02-05', NULL),
(3474, 35, 1, 5, '2025-02-01', 'Hadir'),
(3475, 37, 1, 5, '2025-02-01', 'Hadir'),
(3476, 39, 1, 5, '2025-02-01', 'Hadir'),
(3477, 41, 1, 5, '2025-02-01', 'Hadir'),
(3478, 45, 1, 5, '2025-02-01', 'Hadir'),
(3479, 46, 1, 5, '2025-02-01', 'Hadir'),
(3480, 47, 1, 5, '2025-02-01', 'Hadir'),
(3481, 35, 1, 5, '2025-02-02', NULL),
(3482, 37, 1, 5, '2025-02-02', 'Hadir'),
(3483, 39, 1, 5, '2025-02-02', NULL),
(3484, 41, 1, 5, '2025-02-02', NULL),
(3485, 45, 1, 5, '2025-02-02', NULL),
(3486, 46, 1, 5, '2025-02-02', NULL),
(3487, 47, 1, 5, '2025-02-02', NULL),
(3488, 35, 1, 5, '2025-02-03', NULL),
(3489, 37, 1, 5, '2025-02-03', NULL),
(3490, 39, 1, 5, '2025-02-03', 'Hadir'),
(3491, 41, 1, 5, '2025-02-03', NULL),
(3492, 45, 1, 5, '2025-02-03', NULL),
(3493, 46, 1, 5, '2025-02-03', NULL),
(3494, 47, 1, 5, '2025-02-03', NULL),
(3495, 35, 1, 5, '2025-02-04', NULL),
(3496, 37, 1, 5, '2025-02-04', NULL),
(3497, 39, 1, 5, '2025-02-04', NULL),
(3498, 41, 1, 5, '2025-02-04', 'Hadir'),
(3499, 45, 1, 5, '2025-02-04', NULL),
(3500, 46, 1, 5, '2025-02-04', NULL),
(3501, 47, 1, 5, '2025-02-04', NULL),
(3502, 35, 1, 5, '2025-02-05', NULL),
(3503, 37, 1, 5, '2025-02-05', NULL),
(3504, 39, 1, 5, '2025-02-05', 'Hadir'),
(3505, 41, 1, 5, '2025-02-05', NULL),
(3506, 45, 1, 5, '2025-02-05', NULL),
(3507, 46, 1, 5, '2025-02-05', NULL),
(3508, 47, 1, 5, '2025-02-05', NULL),
(3509, 35, 1, 5, '2025-02-06', NULL),
(3510, 37, 1, 5, '2025-02-06', 'Hadir'),
(3511, 39, 1, 5, '2025-02-06', NULL),
(3512, 41, 1, 5, '2025-02-06', NULL),
(3513, 45, 1, 5, '2025-02-06', NULL),
(3514, 46, 1, 5, '2025-02-06', NULL),
(3515, 47, 1, 5, '2025-02-06', NULL),
(3516, 35, 1, 5, '2025-02-07', 'Hadir'),
(3517, 37, 1, 5, '2025-02-07', 'Hadir'),
(3518, 39, 1, 5, '2025-02-07', 'Hadir'),
(3519, 41, 1, 5, '2025-02-07', 'Hadir'),
(3520, 45, 1, 5, '2025-02-07', 'Hadir'),
(3521, 46, 1, 5, '2025-02-07', 'Hadir'),
(3522, 47, 1, 5, '2025-02-07', 'Hadir'),
(3523, 35, 1, 5, '2025-02-08', NULL),
(3524, 37, 1, 5, '2025-02-08', NULL),
(3525, 39, 1, 5, '2025-02-08', NULL),
(3526, 41, 1, 5, '2025-02-08', NULL),
(3527, 45, 1, 5, '2025-02-08', NULL),
(3528, 46, 1, 5, '2025-02-08', NULL),
(3529, 47, 1, 5, '2025-02-08', NULL),
(3530, 37, 1, 5, '2025-02-09', NULL),
(3531, 39, 1, 5, '2025-02-09', 'Hadir'),
(3532, 41, 1, 5, '2025-02-09', 'Hadir'),
(3533, 45, 1, 5, '2025-02-09', 'Hadir'),
(3534, 46, 1, 5, '2025-02-09', 'Hadir'),
(3535, 47, 1, 5, '2025-02-09', 'Hadir'),
(3536, 37, 1, 5, '2025-02-13', NULL),
(3537, 39, 1, 5, '2025-02-13', 'Hadir'),
(3538, 41, 1, 5, '2025-02-13', 'Hadir'),
(3539, 45, 1, 5, '2025-02-13', 'Hadir'),
(3540, 46, 1, 5, '2025-02-13', 'Hadir'),
(3541, 47, 1, 5, '2025-02-13', 'Hadir'),
(3542, 37, 1, 5, '2025-02-10', 'Hadir'),
(3543, 37, 1, 5, '2025-02-12', 'Hadir'),
(3544, 35, 1, 5, '2025-02-11', 'Hadir'),
(3545, 41, 1, 5, '2025-02-10', 'Hadir'),
(3546, 41, 1, 5, '2025-02-11', 'Hadir'),
(3547, 41, 1, 5, '2025-02-12', 'Hadir'),
(3548, 37, 1, 5, '2025-02-15', NULL),
(3549, 39, 1, 5, '2025-02-15', 'Hadir'),
(3550, 41, 1, 5, '2025-02-15', 'Hadir'),
(3551, 45, 1, 5, '2025-02-15', 'Hadir'),
(3552, 46, 1, 5, '2025-02-15', 'Hadir'),
(3553, 47, 1, 5, '2025-02-15', 'Hadir'),
(3554, 37, 1, 5, '2025-02-19', NULL),
(3555, 39, 1, 5, '2025-02-19', 'Hadir'),
(3556, 41, 1, 5, '2025-02-19', 'Hadir'),
(3557, 45, 1, 5, '2025-02-19', 'Hadir'),
(3558, 46, 1, 5, '2025-02-19', 'Hadir'),
(3559, 47, 1, 5, '2025-02-19', 'Hadir'),
(3560, 37, 1, 5, '2025-02-16', 'Hadir'),
(3561, 37, 1, 5, '2025-02-18', 'Hadir'),
(3562, 35, 1, 5, '2025-02-17', 'Hadir'),
(3563, 41, 1, 5, '2025-02-16', 'Hadir'),
(3564, 41, 1, 5, '2025-02-17', 'Hadir'),
(3565, 41, 1, 5, '2025-02-18', 'Hadir'),
(3566, 35, 1, 5, '2025-02-21', 'Hadir'),
(3567, 37, 1, 5, '2025-02-21', 'Hadir'),
(3568, 39, 1, 5, '2025-02-21', 'Hadir'),
(3569, 41, 1, 5, '2025-02-21', 'Hadir'),
(3570, 45, 1, 5, '2025-02-21', 'Hadir'),
(3571, 46, 1, 5, '2025-02-21', 'Hadir'),
(3572, 47, 1, 5, '2025-02-21', 'Hadir'),
(3573, 35, 1, 5, '2025-02-22', 'Hadir'),
(3574, 37, 1, 5, '2025-02-22', 'Hadir'),
(3575, 39, 1, 5, '2025-02-22', 'Hadir'),
(3576, 41, 1, 5, '2025-02-22', 'Hadir'),
(3577, 45, 1, 5, '2025-02-22', 'Hadir'),
(3578, 46, 1, 5, '2025-02-22', 'Hadir'),
(3579, 47, 1, 5, '2025-02-22', 'Hadir'),
(3580, 35, 1, 5, '2025-02-23', 'Hadir'),
(3581, 35, 1, 5, '2025-02-24', 'Hadir'),
(3582, 35, 1, 5, '2025-02-25', 'Hadir'),
(3583, 39, 1, 5, '2025-02-23', 'Hadir'),
(3584, 39, 1, 5, '2025-02-24', 'Hadir'),
(3585, 39, 1, 5, '2025-02-25', 'Hadir'),
(3586, 39, 1, 5, '2025-02-02', 'Hadir'),
(3587, 41, 1, 5, '2025-02-02', 'Hadir'),
(3588, 45, 1, 5, '2025-02-02', 'Hadir'),
(3589, 46, 1, 5, '2025-02-02', 'Hadir'),
(3590, 47, 1, 5, '2025-02-02', 'Hadir'),
(3591, 39, 1, 5, '2025-02-06', 'Hadir'),
(3592, 41, 1, 5, '2025-02-06', 'Hadir'),
(3593, 45, 1, 5, '2025-02-06', 'Hadir'),
(3594, 46, 1, 5, '2025-02-06', 'Hadir'),
(3595, 47, 1, 5, '2025-02-06', 'Hadir'),
(3596, 39, 1, 5, '2025-02-10', 'Hadir'),
(3597, 41, 1, 5, '2025-02-10', 'Hadir'),
(3598, 45, 1, 5, '2025-02-10', 'Hadir'),
(3599, 46, 1, 5, '2025-02-10', 'Hadir'),
(3600, 47, 1, 5, '2025-02-10', 'Hadir'),
(3601, 39, 1, 5, '2025-02-12', 'Hadir'),
(3602, 41, 1, 5, '2025-02-12', 'Hadir'),
(3603, 45, 1, 5, '2025-02-12', 'Hadir'),
(3604, 46, 1, 5, '2025-02-12', 'Hadir'),
(3605, 47, 1, 5, '2025-02-12', 'Hadir'),
(3606, 39, 1, 5, '2025-02-16', 'Hadir'),
(3607, 41, 1, 5, '2025-02-16', 'Hadir'),
(3608, 45, 1, 5, '2025-02-16', 'Hadir'),
(3609, 46, 1, 5, '2025-02-16', 'Hadir'),
(3610, 47, 1, 5, '2025-02-16', 'Hadir'),
(3611, 39, 1, 5, '2025-02-18', 'Hadir'),
(3612, 41, 1, 5, '2025-02-18', 'Hadir'),
(3613, 45, 1, 5, '2025-02-18', 'Hadir'),
(3614, 46, 1, 5, '2025-02-18', 'Hadir'),
(3615, 47, 1, 5, '2025-02-18', 'Hadir'),
(3616, 35, 1, 5, '2025-02-17', 'Hadir'),
(3617, 37, 1, 5, '2025-02-17', NULL),
(3618, 39, 1, 5, '2025-02-17', NULL),
(3619, 41, 1, 5, '2025-02-17', 'Hadir'),
(3620, 35, 1, 5, '2025-02-28', 'Hadir'),
(3621, 37, 1, 5, '2025-02-28', 'Hadir'),
(3622, 39, 1, 5, '2025-02-28', 'Hadir'),
(3623, 41, 1, 5, '2025-02-28', 'Hadir'),
(3624, 35, 1, 5, '2025-02-27', 'Hadir'),
(3625, 37, 1, 5, '2025-02-27', 'Hadir'),
(3626, 39, 1, 5, '2025-02-27', 'Hadir'),
(3627, 41, 1, 5, '2025-02-27', 'Hadir'),
(3628, 46, 1, 5, '2025-02-27', 'Tidak Hadir'),
(3629, 47, 1, 5, '2025-02-27', 'Tidak Hadir'),
(3630, 46, 1, 5, '2025-02-28', 'Tidak Hadir'),
(3631, 47, 1, 5, '2025-02-28', 'Tidak Hadir'),
(3632, 35, 1, 5, '2025-05-01', NULL),
(3633, 37, 1, 5, '2025-05-01', NULL),
(3634, 39, 1, 5, '2025-05-01', NULL),
(3635, 41, 1, 5, '2025-05-01', NULL),
(3636, 45, 1, 5, '2025-05-01', NULL),
(3637, 46, 1, 5, '2025-05-01', NULL),
(3638, 47, 1, 5, '2025-05-01', NULL),
(3639, 35, 1, 5, '2025-05-02', 'Hadir'),
(3640, 37, 1, 5, '2025-05-02', 'Hadir'),
(3641, 39, 1, 5, '2025-05-02', 'Hadir'),
(3642, 41, 1, 5, '2025-05-02', 'Hadir'),
(3643, 45, 1, 5, '2025-05-02', 'Hadir'),
(3644, 46, 1, 5, '2025-05-02', 'Hadir'),
(3645, 47, 1, 5, '2025-05-02', 'Hadir'),
(3646, 35, 1, 5, '2025-05-03', NULL),
(3647, 37, 1, 5, '2025-05-03', NULL),
(3648, 39, 1, 5, '2025-05-03', NULL),
(3649, 41, 1, 5, '2025-05-03', NULL),
(3650, 45, 1, 5, '2025-05-03', NULL),
(3651, 46, 1, 5, '2025-05-03', NULL),
(3652, 47, 1, 5, '2025-05-03', NULL),
(3653, 35, 1, 5, '2025-05-04', NULL),
(3654, 37, 1, 5, '2025-05-04', NULL),
(3655, 39, 1, 5, '2025-05-04', NULL),
(3656, 41, 1, 5, '2025-05-04', NULL),
(3657, 45, 1, 5, '2025-05-04', NULL),
(3658, 46, 1, 5, '2025-05-04', NULL),
(3659, 47, 1, 5, '2025-05-04', NULL),
(3660, 35, 1, 5, '2025-05-05', NULL),
(3661, 37, 1, 5, '2025-05-05', NULL),
(3662, 39, 1, 5, '2025-05-05', NULL),
(3663, 41, 1, 5, '2025-05-05', NULL),
(3664, 45, 1, 5, '2025-05-05', NULL),
(3665, 46, 1, 5, '2025-05-05', NULL),
(3666, 47, 1, 5, '2025-05-05', NULL),
(3667, 35, 1, 5, '2025-05-06', 'Hadir'),
(3668, 37, 1, 5, '2025-05-06', 'Hadir'),
(3669, 39, 1, 5, '2025-05-06', 'Hadir'),
(3670, 41, 1, 5, '2025-05-06', 'Hadir'),
(3671, 45, 1, 5, '2025-05-06', 'Hadir'),
(3672, 46, 1, 5, '2025-05-06', 'Hadir'),
(3673, 47, 1, 5, '2025-05-06', 'Hadir'),
(3674, 35, 1, 5, '2025-05-07', 'Hadir'),
(3675, 37, 1, 5, '2025-05-07', 'Hadir'),
(3676, 39, 1, 5, '2025-05-07', 'Hadir'),
(3677, 41, 1, 5, '2025-05-07', 'Hadir'),
(3678, 45, 1, 5, '2025-05-07', 'Hadir'),
(3679, 46, 1, 5, '2025-05-07', 'Hadir'),
(3680, 47, 1, 5, '2025-05-07', 'Hadir'),
(3681, 35, 1, 5, '2025-05-08', 'Hadir'),
(3682, 37, 1, 5, '2025-05-08', 'Hadir'),
(3683, 39, 1, 5, '2025-05-08', 'Hadir'),
(3684, 41, 1, 5, '2025-05-08', 'Hadir'),
(3685, 45, 1, 5, '2025-05-08', 'Hadir'),
(3686, 46, 1, 5, '2025-05-08', 'Hadir'),
(3687, 47, 1, 5, '2025-05-08', 'Hadir'),
(3688, 35, 1, 5, '2025-05-09', 'Hadir'),
(3689, 37, 1, 5, '2025-05-09', 'Hadir'),
(3690, 39, 1, 5, '2025-05-09', 'Hadir'),
(3691, 41, 1, 5, '2025-05-09', 'Hadir'),
(3692, 45, 1, 5, '2025-05-09', 'Hadir'),
(3693, 46, 1, 5, '2025-05-09', 'Hadir'),
(3694, 47, 1, 5, '2025-05-09', 'Hadir'),
(3695, 35, 1, 5, '2025-05-10', NULL),
(3696, 37, 1, 5, '2025-05-10', NULL),
(3697, 39, 1, 5, '2025-05-10', NULL),
(3698, 41, 1, 5, '2025-05-10', NULL),
(3699, 45, 1, 5, '2025-05-10', NULL),
(3700, 46, 1, 5, '2025-05-10', NULL),
(3701, 47, 1, 5, '2025-05-10', NULL),
(3702, 35, 1, 5, '2025-05-11', NULL),
(3703, 37, 1, 5, '2025-05-11', NULL),
(3704, 39, 1, 5, '2025-05-11', NULL),
(3705, 41, 1, 5, '2025-05-11', NULL),
(3706, 45, 1, 5, '2025-05-11', NULL),
(3707, 46, 1, 5, '2025-05-11', NULL),
(3708, 47, 1, 5, '2025-05-11', NULL),
(3709, 35, 1, 5, '2025-05-12', NULL),
(3710, 37, 1, 5, '2025-05-12', NULL),
(3711, 39, 1, 5, '2025-05-12', NULL),
(3712, 41, 1, 5, '2025-05-12', NULL),
(3713, 45, 1, 5, '2025-05-12', NULL),
(3714, 46, 1, 5, '2025-05-12', NULL),
(3715, 47, 1, 5, '2025-05-12', NULL),
(3716, 35, 1, 5, '2025-05-13', 'Hadir'),
(3717, 37, 1, 5, '2025-05-13', 'Hadir'),
(3718, 39, 1, 5, '2025-05-13', 'Hadir'),
(3719, 41, 1, 5, '2025-05-13', 'Hadir'),
(3720, 45, 1, 5, '2025-05-13', 'Hadir'),
(3721, 46, 1, 5, '2025-05-13', 'Hadir'),
(3722, 47, 1, 5, '2025-05-13', 'Hadir'),
(3723, 35, 1, 5, '2025-05-14', 'Hadir'),
(3724, 37, 1, 5, '2025-05-14', 'Hadir'),
(3725, 39, 1, 5, '2025-05-14', 'Hadir'),
(3726, 41, 1, 5, '2025-05-14', 'Hadir'),
(3727, 45, 1, 5, '2025-05-14', 'Hadir'),
(3728, 46, 1, 5, '2025-05-14', 'Hadir'),
(3729, 47, 1, 5, '2025-05-14', 'Hadir'),
(3730, 35, 1, 5, '2025-05-15', 'Hadir'),
(3731, 37, 1, 5, '2025-05-15', 'Hadir'),
(3732, 39, 1, 5, '2025-05-15', 'Hadir'),
(3733, 41, 1, 5, '2025-05-15', 'Hadir'),
(3734, 45, 1, 5, '2025-05-15', 'Hadir'),
(3735, 46, 1, 5, '2025-05-15', 'Hadir'),
(3736, 47, 1, 5, '2025-05-15', 'Hadir'),
(3737, 35, 1, 5, '2025-05-16', 'Hadir'),
(3738, 37, 1, 5, '2025-05-16', 'Hadir'),
(3739, 39, 1, 5, '2025-05-16', 'Hadir'),
(3740, 41, 1, 5, '2025-05-16', 'Hadir'),
(3741, 45, 1, 5, '2025-05-16', 'Hadir'),
(3742, 46, 1, 5, '2025-05-16', 'Hadir'),
(3743, 47, 1, 5, '2025-05-16', 'Hadir'),
(3744, 35, 1, 5, '2025-05-17', NULL),
(3745, 37, 1, 5, '2025-05-17', NULL),
(3746, 39, 1, 5, '2025-05-17', NULL),
(3747, 41, 1, 5, '2025-05-17', NULL),
(3748, 45, 1, 5, '2025-05-17', NULL),
(3749, 46, 1, 5, '2025-05-17', NULL),
(3750, 47, 1, 5, '2025-05-17', NULL),
(3751, 35, 1, 5, '2025-05-18', NULL),
(3752, 37, 1, 5, '2025-05-18', NULL),
(3753, 39, 1, 5, '2025-05-18', NULL),
(3754, 41, 1, 5, '2025-05-18', NULL),
(3755, 45, 1, 5, '2025-05-18', NULL),
(3756, 46, 1, 5, '2025-05-18', NULL),
(3757, 47, 1, 5, '2025-05-18', NULL),
(3758, 46, 1, 5, '2025-05-22', 'Hadir'),
(3759, 41, 1, 5, '2025-05-22', 'Hadir'),
(3760, 35, 1, 5, '2025-05-22', 'Hadir'),
(3761, 47, 1, 5, '2025-05-22', 'Hadir'),
(3762, 45, 1, 5, '2025-05-22', 'Hadir'),
(3763, 37, 1, 5, '2025-05-22', 'Tidak Hadir'),
(3764, 39, 1, 5, '2025-05-22', 'Hadir'),
(3765, 46, 1, 5, '2025-06-07', 'Hadir'),
(3766, 41, 1, 5, '2025-06-07', 'Tidak Hadir'),
(3767, 35, 1, 5, '2025-06-07', 'Hadir'),
(3768, 47, 1, 5, '2025-06-07', 'Hadir'),
(3769, 45, 1, 5, '2025-06-07', 'Hadir'),
(3770, 37, 1, 5, '2025-06-07', 'Tidak Hadir'),
(3771, 39, 1, 5, '2025-06-07', 'Hadir'),
(3772, 46, 1, 5, '2025-06-10', 'Hadir'),
(3773, 41, 1, 5, '2025-06-10', 'Hadir'),
(3774, 35, 1, 5, '2025-06-10', 'Hadir'),
(3775, 47, 1, 5, '2025-06-10', 'Tidak Hadir'),
(3776, 45, 1, 5, '2025-06-10', 'Hadir'),
(3777, 37, 1, 5, '2025-06-10', 'Hadir'),
(3778, 39, 1, 5, '2025-06-10', 'Hadir'),
(3779, 46, 1, 5, '2025-06-14', 'Hadir'),
(3780, 41, 1, 5, '2025-06-14', 'Tidak Hadir'),
(3781, 35, 1, 5, '2025-06-14', 'Hadir'),
(3782, 47, 1, 5, '2025-06-14', 'Tidak Hadir'),
(3783, 45, 1, 5, '2025-06-14', 'Hadir'),
(3784, 37, 1, 5, '2025-06-14', 'Hadir'),
(3785, 39, 1, 5, '2025-06-14', 'Tidak Hadir'),
(3786, 46, 1, 5, '2025-06-15', 'Hadir'),
(3787, 56, 1, 5, '2025-06-15', 'Hadir'),
(3788, 57, 1, 5, '2025-06-15', 'Hadir'),
(3789, 41, 1, 5, '2025-06-15', 'Hadir'),
(3790, 35, 1, 5, '2025-06-15', 'Tidak Hadir'),
(3791, 47, 1, 5, '2025-06-15', 'Hadir'),
(3792, 49, 1, 5, '2025-06-15', 'Tidak Hadir'),
(3793, 45, 1, 5, '2025-06-15', 'Hadir'),
(3794, 37, 1, 5, '2025-06-15', 'Hadir'),
(3795, 39, 1, 5, '2025-06-15', 'Tidak Hadir'),
(3796, 54, 1, 5, '2025-06-15', 'Hadir');

-- --------------------------------------------------------

--
-- Table structure for table `classrooms`
--

CREATE TABLE `classrooms` (
  `classroom_id` int(11) NOT NULL,
  `class_name` varchar(50) NOT NULL,
  `tingkatan` int(11) DEFAULT NULL,
  `teacher_id` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `classrooms`
--

INSERT INTO `classrooms` (`classroom_id`, `class_name`, `tingkatan`, `teacher_id`) VALUES
(1, 'Tingkatan 5 Syafie', 5, 10),
(2, 'Tingkatan 5 Maliki', 5, 13),
(3, 'Tingkatan 5 Hambali', 5, 11),
(4, 'Tingkatan 5 Ghazali', 5, 2),
(5, 'Tingkatan 5 Hanafi', 5, 1),
(6, 'Tingkatan 4 Ghazali', 4, 9),
(7, 'Tingkatan 3 Maliki', 3, 15),
(8, 'Tingkatan 4 Syafie', 4, NULL),
(9, 'Tingkatan 3 Syafie', 3, 3),
(10, 'Tingkatan 2 Syafie', 2, 5),
(11, 'Tingkatan 1 Hambali', 1, 14),
(14, 'Tingkatan 1 Syafie', 1, NULL),
(15, 'Tingkatan 1 Ghazali', 1, 7),
(16, 'Tingkatan 2 Ghazali', 2, 4),
(17, 'Tingkatan 3 Ghazali', 3, 12),
(18, 'Tingkatan 2 Hambali', 2, NULL),
(20, 'Tingkatan 4 Hambali', 4, 19),
(21, 'Tingkatan 1 Hanafi', 1, NULL),
(22, 'Tingkatan 2 Hanafi', 2, NULL),
(23, 'Tingkatan 3 Hanafi', 3, 20),
(24, 'Tingkatan 4 Hanafi', 4, NULL),
(25, 'Tingkatan 1 Maliki', 1, NULL),
(26, 'Tingkatan 2 Maliki', 2, NULL),
(27, 'Tingkatan 4 Maliki', 4, 6),
(30, 'Tingkatan 3 Hambali', 3, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `discipline_incidents`
--

CREATE TABLE `discipline_incidents` (
  `incident_id` int(11) NOT NULL,
  `student_id` int(11) NOT NULL,
  `teacher_id` int(11) NOT NULL,
  `incident_date` date NOT NULL,
  `incident_type` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `action_taken` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `severity` enum('Ringan','Sederhana','Berat') DEFAULT 'Ringan',
  `status` enum('Baru','Dalam Tindakan','Selesai') DEFAULT 'Baru'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Student discipline incidents';

--
-- Dumping data for table `discipline_incidents`
--

INSERT INTO `discipline_incidents` (`incident_id`, `student_id`, `teacher_id`, `incident_date`, `incident_type`, `description`, `action_taken`, `created_at`, `updated_at`, `severity`, `status`) VALUES
(1, 41, 1, '2025-06-07', 'Membawa Telefon Bimbit Tanpa Kebenaran', 'Menggunakan telefon bimbit semasa perhimpunan', 'Berjumpa dengan guru disiplin', '2025-06-07 14:00:13', '2025-06-15 05:48:25', 'Sederhana', 'Baru'),
(2, 46, 1, '2025-06-07', 'Bergaduh', 'uidye8f9utg7l8', 'sudyhfoiweuf', '2025-06-07 14:01:01', '2025-06-07 14:01:01', 'Berat', 'Baru'),
(3, 45, 1, '2025-06-07', 'Buli', 'uiyhf9eiosfwe90', 'sdifh;owelfkls', '2025-06-07 14:02:51', '2025-06-10 03:25:20', 'Berat', 'Selesai'),
(4, 45, 1, '2025-06-07', 'Meniru Semasa Peperiksaan', 'assalamualaikum', 'efert45fsdc', '2025-06-07 14:36:23', '2025-06-07 15:06:14', 'Berat', 'Selesai'),
(5, 35, 1, '2025-06-10', 'Merokok', 'abcdhsgdsud', 'jsdkcfhisd', '2025-06-10 03:43:42', '2025-06-10 03:43:42', 'Berat', 'Baru'),
(6, 41, 1, '2025-06-10', 'Ponteng Kelas', 'Ponteng Kelas Bahasa Melayu', 'Salin 3 karangan', '2025-06-10 03:44:24', '2025-06-15 16:04:26', 'Ringan', 'Selesai'),
(7, 33, 8, '2025-06-10', 'Membawa Telefon Bimbit Tanpa Kebenaran', 'blablablabla', 'Panggil ibubapa', '2025-06-10 12:15:44', '2025-06-15 13:32:25', 'Sederhana', 'Baru'),
(8, 43, 1, '2025-06-15', 'Tidak Memakai Pakaian Seragam Lengkap', 'Tidak memakai pakaian seragam sekolah', 'Surat amaran', '2025-06-15 16:11:31', '2025-06-15 16:11:31', 'Ringan', 'Baru');

-- --------------------------------------------------------

--
-- Table structure for table `parents`
--

CREATE TABLE `parents` (
  `parent_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `full_name` varchar(100) DEFAULT NULL,
  `phone_number` varchar(20) DEFAULT NULL,
  `address` varchar(255) DEFAULT NULL,
  `email_verified` tinyint(1) DEFAULT 0,
  `verification_token` varchar(255) DEFAULT NULL,
  `token_created_at` datetime DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `status_pibg` enum('dah bayar','belum bayar') DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `parents`
--

INSERT INTO `parents` (`parent_id`, `user_id`, `full_name`, `phone_number`, `address`, `email_verified`, `verification_token`, `token_created_at`, `email`, `status_pibg`) VALUES
(21, 37, 'Nor Binti Lina', '0198765432', '101 Jalan Pantai, Batu Pahat', 1, NULL, NULL, NULL, NULL),
(22, 38, 'Hassan Bin Musa', '0187654321', '202 Jalan Kebun, Batu Pahat', 1, NULL, NULL, '<EMAIL>', NULL),
(23, 39, 'Fatimah Binti Zahar', '0161234567', '303 Jalan Kemboja, Batu Pahat', 1, NULL, NULL, NULL, NULL),
(24, 40, 'Zainab Binti Hassan', '0179876543', '404 Jalan Raya, Batu Pahat', 1, NULL, NULL, NULL, NULL),
(25, 42, 'Rohana Binti Mohamad', '0131234567', '606 Jalan Seri, Batu Pahat', 1, NULL, NULL, NULL, NULL),
(26, 43, 'Mohamed Bin Ahmad', '0169876543', '707 Jalan Cempaka, Batu Pahat', 1, NULL, NULL, NULL, NULL),
(27, 44, 'Jamaludin Bin Mohd', '0171112233', '808 Jalan Jaya, Batu Pahat', 1, NULL, NULL, '<EMAIL>', NULL),
(28, 45, 'Roslina Binti Ahmad', '0132345678', '909 Jalan Lintang, Batu Pahat', 1, NULL, NULL, '<EMAIL>', NULL),
(29, 46, 'Tengku Fatimah Binti', '0194455667', '1010 Jalan Teratai, Batu Pahat', 1, NULL, NULL, NULL, NULL),
(30, 48, 'Norain Binti Mohamad', '0172233445', '1212 Jalan Bahagia, Batu Pahat', 1, NULL, NULL, NULL, NULL),
(31, 49, 'Halimah Binti Ali', '0185566778', '1313 Jalan Harmoni, Batu Pahat', 1, NULL, NULL, NULL, NULL),
(32, 50, 'Nazri Bin Mohd', '0139876543', '1414 Jalan Timur, Batu Pahat', 1, NULL, NULL, NULL, NULL),
(33, 52, 'Nabila Binti Zulkifli', '0115544332', '1616 Jalan Selamat, Batu Pahat', 1, NULL, NULL, NULL, NULL),
(34, 53, 'Kamarul Bin Mohd', '0166789123', '1717 Jalan Sejahtera, Batu Pahat', 1, NULL, NULL, NULL, NULL),
(54, 159, 'Zubaile Bin Abdullah', '0197545727', 'Pura Kencana', 1, NULL, NULL, NULL, NULL),
(55, 160, 'Juliyana Selamat', '0197545727', 'Pura Kencana', 1, NULL, NULL, NULL, NULL),
(56, 161, 'Ibrahim Bin Muntawil', '01876767737', '17 Jalan Lombong, Batu Pahat', 1, NULL, NULL, NULL, NULL),
(59, 164, 'Johari Bin Abdul', '01876767737', '17 Jalan Lombong, Batu Pahat', 1, NULL, NULL, NULL, NULL),
(60, 165, 'Siti Sofea Binti Solah', '0187563342', '17 Seri Mutiara, Batu Pahat', 1, NULL, NULL, NULL, NULL),
(61, 169, 'Fairuz bin Kamaruddin', '0125006011', 'Taman Sri Rambai', 1, '', '0000-00-00 00:00:00', NULL, NULL),
(62, 170, 'Emynorwana Kamaruddin', '0123456789', '123, Taman Muhibbah', 1, NULL, NULL, NULL, NULL),
(63, 179, 'Norfaizah binti Abdul', '0198723456', 'Batu Pahat', 1, '', '2025-06-10 05:49:43', NULL, NULL),
(64, 181, 'Kamariah binti Abu', '0187654389', 'Batu Pahat', 1, NULL, NULL, NULL, NULL),
(65, 183, 'Kamal binti Abu', '0187654389', 'Batu Pahat', 1, NULL, NULL, NULL, NULL),
(66, 184, 'wuiyeqwru', '882917489591', 'jkhedioancddild', 0, 'b43e7257c22b26d17b8edcbc61044b7b', '2025-06-13 04:19:43', NULL, NULL),
(67, 185, 'UWIYRIQWORH', '7869980900', 'ASKJHDSEFLVLI', 0, 'c9e83f346687d955466fccf85f145fc4', '2025-06-13 04:38:52', NULL, NULL),
(68, 196, 'Afiqah binti Omar', '0198768900', 'Taman Meranti', 1, NULL, NULL, NULL, NULL),
(69, 198, 'Firdauz bin Kamaruddin', '0127686533', 'Taman Nusa Intan', 1, NULL, NULL, NULL, NULL),
(70, 200, 'Fifiadzly bin Kamaruddin', '0187677865', 'Taman Sungai Abong', 1, NULL, NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `roles`
--

CREATE TABLE `roles` (
  `role_id` int(11) NOT NULL,
  `role_name` varchar(50) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `roles`
--

INSERT INTO `roles` (`role_id`, `role_name`) VALUES
(1, 'Admin'),
(2, 'Parent'),
(4, 'Student'),
(3, 'Teacher');

-- --------------------------------------------------------

--
-- Table structure for table `schedules`
--

CREATE TABLE `schedules` (
  `schedule_id` int(11) NOT NULL,
  `day_name` enum('Isnin','Selasa','Rabu','Khamis','Jumaat') NOT NULL,
  `time_slot` varchar(20) NOT NULL,
  `teacher_id` int(11) NOT NULL,
  `subject_id` int(11) NOT NULL,
  `classroom_id` int(11) NOT NULL,
  `academic_year` varchar(9) NOT NULL DEFAULT '2024',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `schedules`
--

INSERT INTO `schedules` (`schedule_id`, `day_name`, `time_slot`, `teacher_id`, `subject_id`, `classroom_id`, `academic_year`, `created_at`, `updated_at`) VALUES
(1, 'Isnin', '07:30-08:30', 2, 2, 4, '2025/2026', '2025-06-07 15:42:16', '2025-06-07 15:42:16'),
(119, 'Isnin', '09:00-09:30', 1, 1, 14, '2025/2026', '2025-06-15 05:13:45', '2025-06-15 05:13:45'),
(120, 'Isnin', '09:30-10:00', 1, 1, 14, '2025/2026', '2025-06-15 05:13:45', '2025-06-15 05:13:45'),
(161, 'Selasa', '09:00-09:30', 5, 6, 5, '2025/2026', '2025-06-15 16:03:48', '2025-06-15 16:03:48'),
(162, 'Selasa', '09:30-10:00', 5, 6, 5, '2025/2026', '2025-06-15 16:03:48', '2025-06-15 16:03:48'),
(163, 'Selasa', '10:30-11:00', 14, 2, 5, '2025/2026', '2025-06-15 16:03:48', '2025-06-15 16:03:48'),
(164, 'Selasa', '11:00-11:30', 14, 2, 5, '2025/2026', '2025-06-15 16:03:48', '2025-06-15 16:03:48'),
(165, 'Rabu', '07:30-08:00', 1, 1, 5, '2025/2026', '2025-06-15 16:03:48', '2025-06-15 16:03:48'),
(166, 'Rabu', '08:00-08:30', 1, 1, 5, '2025/2026', '2025-06-15 16:03:48', '2025-06-15 16:03:48'),
(167, 'Isnin', '08:00-08:30', 1, 1, 5, '2025/2026', '2025-06-15 16:03:48', '2025-06-15 16:03:48'),
(168, 'Isnin', '08:30-09:00', 1, 1, 5, '2025/2026', '2025-06-15 16:03:48', '2025-06-15 16:03:48'),
(169, 'Jumaat', '08:00-08:30', 5, 6, 5, '2025/2026', '2025-06-15 16:03:48', '2025-06-15 16:03:48'),
(170, 'Jumaat', '08:30-09:00', 5, 6, 5, '2025/2026', '2025-06-15 16:03:48', '2025-06-15 16:03:48'),
(171, 'Jumaat', '10:30-11:00', 1, 1, 5, '2025/2026', '2025-06-15 16:03:48', '2025-06-15 16:03:48'),
(172, 'Jumaat', '11:00-11:30', 1, 1, 5, '2025/2026', '2025-06-15 16:03:48', '2025-06-15 16:03:48');

-- --------------------------------------------------------

--
-- Table structure for table `students`
--

CREATE TABLE `students` (
  `student_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `full_name` varchar(100) DEFAULT NULL,
  `no_ic` varchar(14) DEFAULT NULL,
  `gender` enum('Lelaki','Perempuan') DEFAULT NULL,
  `birth_date` date DEFAULT NULL,
  `parent_id` int(11) NOT NULL,
  `relation` enum('Ibu','Bapa','Penjaga') DEFAULT NULL,
  `classroom_id` int(11) DEFAULT NULL,
  `age` int(11) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `pibg_paid` tinyint(1) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `students`
--

INSERT INTO `students` (`student_id`, `user_id`, `full_name`, `no_ic`, `gender`, `birth_date`, `parent_id`, `relation`, `classroom_id`, `age`, `email`, `pibg_paid`) VALUES
(32, 126, 'Firkhan Bin Zulkifli', '090312-14-1235', 'Lelaki', '2009-03-12', 23, 'Ibu', 3, 16, '<EMAIL>', 1),
(33, 127, 'Salmiah Binti Zulkifli', '081220-05-2246', 'Perempuan', '2008-12-20', 23, 'Ibu', 1, 17, NULL, 1),
(34, 128, 'Shahreen Binti Zainal', '090930-02-1024', 'Perempuan', '2009-09-30', 28, 'Ibu', 3, 16, NULL, 1),
(35, 129, 'Hafiz Bin Jamaludin', '081015084561', 'Lelaki', '2008-10-15', 27, 'Bapa', 5, 17, NULL, 1),
(36, 130, 'Zulaikha Binti Mohamed', '091111-01-3348', 'Perempuan', '2009-11-11', 26, 'Bapa', NULL, 16, NULL, 0),
(37, 131, 'Rohana Binti Nazri', '081001036652', 'Perempuan', '2008-10-01', 32, 'Bapa', 5, 17, NULL, 1),
(38, 132, 'Imran Bin Kamarul', '090216-11-7789', 'Lelaki', '2009-02-16', 34, 'Bapa', NULL, 16, NULL, 0),
(39, 133, 'Syafiq Bin Hassan', '081105136571', 'Lelaki', '2008-11-05', 22, 'Bapa', 5, 17, NULL, 0),
(40, 134, 'Nabila Binti Hassan', '090417-09-4428', 'Perempuan', '2009-04-17', 22, 'Bapa', NULL, 16, NULL, 0),
(41, 135, 'Faris Bin Zainal', '081230067813', 'Lelaki', '2008-12-30', 28, 'Ibu', 5, 17, NULL, 1),
(43, 138, 'Amin Irfan Bin Nazri', '091229-03-6651', 'Lelaki', '2009-12-29', 32, 'Bapa', 6, 16, NULL, 0),
(44, 139, 'Hajar Aminah Binti Nazri', '111001-03-6652', 'Perempuan', '2011-10-01', 32, 'Bapa', NULL, 13, NULL, 0),
(45, 166, 'Nasha Binti Ibrahim', '081230067814', 'Perempuan', '2008-12-30', 56, 'Bapa', 5, 17, NULL, 0),
(46, 167, 'Aisyah Binti Johari', '	08102101581', 'Perempuan', '2008-10-21', 59, 'Bapa', 5, 17, NULL, 1),
(47, 168, 'Irfan Bin Nedim', '080105136521', 'Lelaki', '2008-01-05', 60, 'Ibu', 5, 17, NULL, 1),
(49, 172, 'Muhammad Faiq bin Mohd Fairiz', '081111015555', 'Lelaki', '2008-11-11', 62, 'Ibu', 5, 17, NULL, 0),
(53, 178, 'Sabrina binti Ali', '090321015125', 'Perempuan', '2009-03-21', 31, 'Ibu', NULL, 16, NULL, 0),
(54, 186, 'Hakim bin Osman', '080801018777', 'Lelaki', '2008-08-08', 30, 'Ibu', 5, 17, NULL, 0),
(55, 187, 'Mariam binti Osman', '100202018888', 'Perempuan', '2010-02-22', 30, 'Ibu', NULL, 15, NULL, 0),
(56, 188, 'Faiz bin Jamal', '080103018999', 'Lelaki', '2008-01-03', 25, 'Ibu', 5, 17, NULL, 0),
(57, 189, 'Faizah bin Jamal', '080103016754', 'Perempuan', '2008-01-03', 25, 'Ibu', 5, 17, NULL, 0),
(58, 194, 'Faizz bin Jamal', '080103018977', 'Lelaki', '2010-01-03', 25, 'Ibu', NULL, 15, NULL, 0),
(59, 197, 'Safiyya binti Ahmad', '081212046778', 'Perempuan', '2008-12-12', 68, 'Ibu', NULL, 17, NULL, 0);

-- --------------------------------------------------------

--
-- Table structure for table `student_subjects`
--

CREATE TABLE `student_subjects` (
  `student_id` int(11) NOT NULL,
  `subject_id` int(11) NOT NULL,
  `classroom_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `student_subjects`
--

INSERT INTO `student_subjects` (`student_id`, `subject_id`, `classroom_id`) VALUES
(32, 1, 3),
(32, 14, 3),
(33, 2, 1),
(33, 23, 1),
(34, 1, 3),
(34, 14, 3),
(35, 1, 5),
(35, 2, 5),
(35, 6, 5),
(37, 1, 5),
(37, 2, 5),
(37, 6, 5),
(39, 1, 5),
(39, 2, 5),
(39, 6, 5),
(41, 1, 5),
(41, 2, 5),
(41, 6, 5),
(43, 2, 6),
(45, 1, 5),
(45, 2, 5),
(45, 6, 5),
(46, 1, 5),
(46, 2, 5),
(46, 6, 5),
(47, 1, 5),
(47, 2, 5),
(47, 6, 5),
(54, 1, 5);

-- --------------------------------------------------------

--
-- Table structure for table `subjects`
--

CREATE TABLE `subjects` (
  `subject_id` int(11) NOT NULL,
  `subject_name` varchar(100) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `subjects`
--

INSERT INTO `subjects` (`subject_id`, `subject_name`) VALUES
(2, 'Bahasa Inggeris'),
(1, 'Bahasa Melayu'),
(12, 'Biologi'),
(25, 'Fizik'),
(7, 'Geografi'),
(8, 'Kemahiran Hidup'),
(11, 'Kimia'),
(3, 'Matematik'),
(10, 'Matematik Tambahan'),
(6, 'Pendidikan Islam'),
(9, 'Pendidikan Moral'),
(14, 'Perakaunan'),
(4, 'Sains'),
(5, 'Sejarah'),
(23, 'Teknologi ICT 2');

-- --------------------------------------------------------

--
-- Table structure for table `teachers`
--

CREATE TABLE `teachers` (
  `teacher_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `full_name` varchar(100) DEFAULT NULL,
  `gender` enum('Lelaki','Perempuan') DEFAULT NULL,
  `staff_id` varchar(50) DEFAULT NULL,
  `phone_number` varchar(20) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `teachers`
--

INSERT INTO `teachers` (`teacher_id`, `user_id`, `full_name`, `gender`, `staff_id`, `phone_number`, `email`) VALUES
(1, 2, 'Cikgu Aminah Binti Abdullah', 'Perempuan', 'TCH001', '0197545680', '<EMAIL>'),
(2, 3, 'Cikgu Ahmad Bin Ali', 'Lelaki', 'TCH010', '0123456790', NULL),
(3, 4, 'Cikgu Siti Aisyah Binti Abdullah', 'Perempuan', 'TCH003', '0123456790', NULL),
(4, 5, 'Cikgu Mohd Faizal Bin Hashim', 'Lelaki', 'TCH004', '0123456791', NULL),
(5, 6, 'Cikgu Nurul Huda Binti Razak', 'Perempuan', 'TCH005', '0123456792', NULL),
(6, 7, 'Cikgu Zulkifli Bin Kamaruddin', 'Lelaki', 'TCH006', '0123456793', NULL),
(7, 8, 'Cikgu Nur Aisyah Binti Mohd', 'Perempuan', 'TCH007', '0123456794', NULL),
(8, 9, 'Cikgu Ali Bin Mansor', 'Lelaki', 'TCH008', '0123456780', NULL),
(9, 10, 'Cikgu Liyana Binti Zainal', 'Perempuan', 'TCH009', '0123456796', NULL),
(10, 12, 'Cikgu Farah Diana Binti Mohamad', 'Perempuan', 'TCH011', '0123456798', NULL),
(11, 13, 'Cikgu Sulaiman Bin Ahmad', 'Lelaki', 'TCH013', '0127877898', NULL),
(12, 54, 'Cikgu Suriani Binti Ishak', 'Perempuan', 'TCH014', '0197767372', NULL),
(13, 136, 'Cikgu Jauhari Bin Ahmad Nizam', 'Lelaki', 'TCH002', '0198799832', NULL),
(14, 173, 'Cikgu Amira Binti Kamal', 'Perempuan', 'TCH102', '0189765789', NULL),
(15, 177, 'Cikgu Danish Bin Mazlan', 'Lelaki', 'TCH012', '0190012345', NULL),
(16, 190, 'Dalila binti Rizwan', 'Perempuan', 'TCH016', '0196754238', NULL),
(17, 191, 'Salwa binti Aidil', 'Perempuan', 'TCH017', '0167823899', NULL),
(18, 192, 'Nasrul bin Faiz', 'Lelaki', 'TCH018', '0196574300', NULL),
(19, 193, 'Cikgu Badrul bin Isa', 'Lelaki', 'TCH019', '0127685677', NULL),
(20, 202, 'Cikgu Arliyana binti Mokhtar', 'Perempuan', 'TCH020', '0197865456', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `teacher_subject_classrooms`
--

CREATE TABLE `teacher_subject_classrooms` (
  `teacher_id` int(11) NOT NULL,
  `subject_id` int(11) NOT NULL,
  `classroom_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `teacher_subject_classrooms`
--

INSERT INTO `teacher_subject_classrooms` (`teacher_id`, `subject_id`, `classroom_id`) VALUES
(1, 1, 5),
(1, 1, 14),
(1, 3, 17),
(1, 14, 3),
(2, 2, 7),
(2, 5, 4),
(2, 8, 21),
(3, 3, 2),
(4, 4, 1),
(4, 5, 11),
(5, 2, 17),
(5, 6, 5),
(6, 7, 4),
(7, 9, 2),
(8, 2, 6),
(8, 8, 7),
(9, 4, 17),
(9, 10, 6),
(10, 11, 1),
(10, 12, 6),
(10, 14, 24),
(11, 12, 3),
(12, 1, 7),
(12, 1, 10),
(12, 2, 21),
(13, 1, 17),
(13, 2, 1),
(13, 23, 1),
(14, 2, 5),
(15, 6, 17),
(19, 12, 8),
(20, 8, 14);

-- --------------------------------------------------------

--
-- Table structure for table `time_slots`
--

CREATE TABLE `time_slots` (
  `slot_id` int(11) NOT NULL,
  `slot_name` varchar(20) NOT NULL,
  `start_time` time NOT NULL,
  `end_time` time NOT NULL,
  `is_break` tinyint(1) NOT NULL DEFAULT 0,
  `display_order` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `time_slots`
--

INSERT INTO `time_slots` (`slot_id`, `slot_name`, `start_time`, `end_time`, `is_break`, `display_order`) VALUES
(1, 'Perhimpunan', '07:30:00', '08:00:00', 0, 1),
(2, 'Waktu 1', '08:00:00', '08:30:00', 0, 2),
(3, 'Waktu 2', '08:30:00', '09:00:00', 0, 3),
(4, 'Waktu 3', '09:00:00', '09:30:00', 0, 4),
(5, 'Waktu 4', '09:30:00', '10:00:00', 0, 5),
(6, 'Rehat', '10:00:00', '10:30:00', 1, 6),
(7, 'Waktu 5', '10:30:00', '11:00:00', 0, 7),
(8, 'Waktu 6', '11:00:00', '11:30:00', 0, 8),
(9, 'Waktu 7', '11:30:00', '12:00:00', 0, 9),
(10, 'Waktu 8', '12:00:00', '12:30:00', 0, 10),
(11, 'Waktu 9', '12:30:00', '13:00:00', 0, 11);

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `user_id` int(11) NOT NULL,
  `email` varchar(100) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `role_id` int(11) NOT NULL,
  `failed_attempts` int(11) DEFAULT 0,
  `lockout_time` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`user_id`, `email`, `password_hash`, `role_id`, `failed_attempts`, `lockout_time`) VALUES
(1, '<EMAIL>', '$2y$10$yDitkpWRMjLZmNgOjrog..MeECyW5D24M1TLRy7VzAo4MXdhm5BZW', 1, 0, NULL),
(2, '<EMAIL>', '$2y$10$Vv2FcQV53kZKENjRiB7id.npTdmaVq00mzvrxj0vDz9zWw4oCnR5G', 3, 0, NULL),
(3, '<EMAIL>', '$2y$10$ygMthxS75uajAKSkeStneOL6DWJKfMTZVaM/ztx5XKna/AI6ghWz6', 3, 2, NULL),
(4, '<EMAIL>', '$2y$10$55.0QOvffuKX/gokVBRID.aVopRsPLV1dEt2V0h9sEBOjZ9aMDr6K', 3, 0, NULL),
(5, '<EMAIL>', '$2y$10$HgRZUMIiost72V5t1CWtH./efMbM./HcZXhPmZunFMkn7XazLl//K', 3, 0, NULL),
(6, '<EMAIL>', '$2y$10$mj1Y9Nn89vVzTjmKpBs.LOl5pwV4/OAnikWwBTUSRCC1LDjRh520G', 3, 0, NULL),
(7, '<EMAIL>', '$2y$10$Tr1pPYbcbIcEzPtAcwPRaeSh.ytXvLGMM0lm/8SRQUlGHCcaU8NkG', 3, 0, NULL),
(8, '<EMAIL>', '$2y$10$jXsb4QkSNTWkCT47vbwvZezcMKavFfjHGGWDnNvELS//VPv2l/8mS', 3, 0, NULL),
(9, '<EMAIL>', '$2y$10$jm4Qp.afLCco44CcIXZMbOSLV1lCXT0oYp5ApIycfzPTE6XRw5tZe', 3, 0, NULL),
(10, '<EMAIL>', '$2y$10$.oqyCQCOZs1KtvJNDZTS6uPTkaxTUgT0cqo5gIutOMARP4GBhrUcO', 3, 0, NULL),
(12, '<EMAIL>', '$2y$10$tlfwhNCk4UXhRjMcbvG/ZOc/6tEeUK404YCGL.z0/YeWOrOiS.G.G', 3, 0, NULL),
(13, '<EMAIL>', '$2y$10$VV9bUWDHFSGesFP8nsqsKecjtwbBGAjoXnOnyUeJKLZSzXGlXG6wS', 3, 0, NULL),
(37, '<EMAIL>', '$2y$10$p8ajNnffdOkx/ntPdkdT8uoREtKo98XZqYStO.3dZs/KnPHpPXFtq', 2, 0, NULL),
(38, '<EMAIL>', '$2y$10$7URW7oArE22bPeeHobx/c.0KRQE87Ijz/uUbrV17NI9ImYyviCMaO', 2, 0, NULL),
(39, '<EMAIL>', '$2y$10$9hVmwRnp9.ikV8NMY5RDTeOwXbthNVNdJKW/tNNejAxB8wOfxkU1u', 2, 0, NULL),
(40, '<EMAIL>', '$2y$10$O5Wv0zWQq3UN.bqsM2WH9eqbPZcZGr31IwUvemwjs95aWrztbOPGu', 2, 0, NULL),
(42, '<EMAIL>', '$2y$10$NZa1rQJIAUANdcIcNge/CuoDRZXSG4YlWP3qEVnWLeBLCvsGxgVDu', 2, 1, NULL),
(43, '<EMAIL>', '$2y$10$KBpZKNlFfb7tfeJUZWwMbeKHBI4O230SW1Ph7DVxyYzMkoPk4NYme', 2, 0, NULL),
(44, '<EMAIL>', '$2y$10$JMhZ77ltkOowMnGzjX1EreYqIMlA.2j1LDNuYTnHqbt62a1GEKkIa', 2, 0, NULL),
(45, '<EMAIL>', '$2y$10$X9ZsgsCTtruQwwby8uujqOCe55Ih67kKyxH.o2JPQ0oUADy/7IRNu', 2, 0, NULL),
(46, '<EMAIL>', '$2y$10$Kpd9WCUNuf2I47aDrTiEfODH1q4PcaL8/Nn9O/8mkkWDWUZf0FCCy', 2, 0, NULL),
(48, '<EMAIL>', '$2y$10$14bRZ3XRDqy6BD4.cchyze63thoJVqdNqPzG8EDmWq57MIMzUjku.', 2, 0, NULL),
(49, '<EMAIL>', '$2y$10$x1ZP59tJRVS9fRkCvukwr.Fs2TBQ/NO70RQ5ut2SqpVIiqP/gxhjG', 2, 0, NULL),
(50, '<EMAIL>', '$2y$10$NcAO4hkWlvbegKRfKcZY3.WmN31VSeUnF960jrWed3QPHtbwYEjs.', 2, 0, NULL),
(52, '<EMAIL>', '$2y$10$fW0YQJc6QM4IDISjqCgcl.ovrsPGqgn2pNltH4BAdT1s5F56QTGla', 2, 0, NULL),
(53, '<EMAIL>', '$2y$10$9X4ZMKchV1HEj0UFodPzEuxivTk6.z/.ZhG.Fk26YaY7DPKpK6i.a', 2, 0, NULL),
(54, '<EMAIL>', '$2y$10$oUaVnt8dbzbceha2npJH9ubgYZMwnohkdwmQdE7RVetaqQ6y/qM86', 3, 0, NULL),
(126, '<EMAIL>', '$2y$10$P5Co3sh03t2DwumXGWVTfelMQrquQbu3SxNOx5hL29coh3JajBtgq', 4, 2, NULL),
(127, '<EMAIL>', '$2y$10$ljA9DlEc09tjb/b8v3WnAuiO/zSmO5k7ca5t8U7frCYT2tQZjwuca', 4, 0, NULL),
(128, '<EMAIL>', '$2y$10$xAAlrKPHuGYUgm1PCIU8/.0rL1mNJnRj4lHT7eOzX3Ji1Ws5hxEcG', 4, 0, NULL),
(129, '<EMAIL>', '$2y$10$xhNe.SqjoZFrYb5lsYIV8.vDtKG8eljbsBIJ1EUKJqcG2vQWDjQJe', 4, 0, NULL),
(130, '<EMAIL>', '$2y$10$4nlz24eJGDaqFHUwIAmH6u57K4x.Q/B9pjdSkWYft/iE5zHuVi0GO', 4, 0, NULL),
(131, '<EMAIL>', '$2y$10$RdLtm7Swb2wHSTYcQld09OYUnlFXmeebi.ISobEhywKIIfmL4WknK', 4, 0, NULL),
(132, '<EMAIL>', '$2y$10$Wy/cQ.OWrc8Nk/ZhbZTeeOF3MQhs1ILBRVHEw3DeKnVLgF2guYBPu', 4, 0, NULL),
(133, '<EMAIL>', '$2y$10$ZBigetpXyFCOjgZeY6Z7kOX8EDNW/ScvQea.tcQViQUMi1s.50hIK', 4, 0, NULL),
(134, '<EMAIL>', '$2y$10$vHEUHgWyXxeG4.sGtewYT.tcMNPmJrAgIPeCDUsBsm13k3EJ3OYEa', 4, 0, NULL),
(135, '<EMAIL>', '$2y$10$6HPhl6m54OyjZCX3jWKHOOpt5GqY0NBKP/PSYzHFUcQzw72DeF9di', 4, 0, NULL),
(136, '<EMAIL>', '$2y$10$VW.45e2UN4fTVK/MqWZY9u75NL7sUQ7DhLrl/QH6azCkb06Ly3XMi', 3, 0, NULL),
(138, '<EMAIL>', '$2y$10$T9Aa2SbFVqrrKeOEk7KBYOrakTHvJsM56RfeDBtlSygVXCyTrvH8O', 4, 0, NULL),
(139, '<EMAIL>', '$2y$10$id574VQnQmGHPD4clRBOreh1On1f09RtgazBj8HtQX9qw7p7UGv3i', 4, 0, NULL),
(159, '<EMAIL>', '$2y$10$L8f/l2vr0tXoAVxkcGN3feMKznCKQWqvakFli2M/INL2i0ysLH3vm', 2, 0, NULL),
(160, '<EMAIL>', '$2y$10$jGXu.wZhDs0nkX23k4lYZ.4xGoXc7F3/X.DP418fp9XTPnYLkihNq', 2, 0, NULL),
(161, '<EMAIL>', '$2y$10$iOOoX.5DA5YDobhXEypvmeibsQ3flPHfLp67InUIL6ySwQ/lfztbK', 2, 0, NULL),
(164, '<EMAIL>', '$2y$10$6w1m5Q8guvlFvwGhILsZnu.Fqj/AYl1HCJVutxI3/ldC2VEWmYvc.', 2, 0, NULL),
(165, '<EMAIL>', '$2y$10$RUFYZSkliDA5srFbRn3vh.k3I66K3H/rsu0P.Z5DtMLEVMtVam8Hq', 2, 0, NULL),
(166, '<EMAIL>', '$2y$10$pESP8myr.YemH1LjbUSFEuVVQvDg0HxUUD4tqFbkKIEFYI2eicKWS', 4, 0, NULL),
(167, '<EMAIL>', '$2y$10$.zo82C2Rj9r6F8LFhzcike9kn9mdygHZJcV1NICI22aCUbBVW8aD2', 4, 0, NULL),
(168, '<EMAIL>', '$2y$10$jiFxeNLMBKJQel1onWpLae2pWmA2dd.Db0ZZB0wDgsWbbJfvG/Yta', 4, 0, NULL),
(169, '<EMAIL>', '$2y$10$Y2ISDwsiaxbuGOUUC1w0AOyaeE55sI.IFt2K9ihyVU6.B7POMp6Ca', 2, 0, NULL),
(170, '<EMAIL>', '$2y$10$qI0u1oqAkLvCd9Yf2DBwSev.2k43mgww1.jBj.aP7Bd6TWfVQkYgm', 2, 0, NULL),
(172, '<EMAIL>', '$2y$10$OtXF88gA7eG1vdCSGMw3ruzFQam1U6d6e2dNuYc1cjix/clWjHtl6', 4, 0, NULL),
(173, '<EMAIL>', '$2y$10$Eb.Im8/8nRfNrJfhF4nsE.6rx07eaTBa9WwudlvGTXMdaFOSGRk9q', 3, 0, NULL),
(174, '<EMAIL>', '$2y$10$yiLVT8COSQ4tFd6/wl0rJusucp0/yNk3O3.KAKOAPZvvtnImdF2xW', 4, 0, NULL),
(175, '<EMAIL>', '$2y$10$645tRicS3V23sJ6mfnk0EOF3E2jGW9IE5V0xRuweNHvqtMtfyAAYC', 4, 0, NULL),
(177, '<EMAIL>', '$2y$10$24OlMoK.Mc.RG3Y7arXLh.oDk/WX98.0SZxjr1nLC1.P5faxM9SaC', 3, 0, NULL),
(178, '<EMAIL>', '$2y$10$1qbWpldh.NQvfJ58.xjuqu9Z0yOrV86qDCN4VsJDfA6ORJ3I5vZqe', 4, 0, NULL),
(179, '<EMAIL>', '$2y$10$i3lNrj.xw062LbxX3KxQ1.YZ8fwEg01RZdudjzO1Poe.6NcCKfk72', 2, 0, NULL),
(181, '<EMAIL>', '$2y$10$7b6wjs0ka/72RAywrNGBreCtYrbCkgSbrAWnA3ZwCIvkLqSGaDasq', 2, 0, NULL),
(183, '<EMAIL>', '$2y$10$mC9iIafXZWDvxgYvTnEqYO9MGOzGXXVZa8t2x5YDGKRUVMD89HsKO', 2, 0, NULL),
(184, '<EMAIL>', '$2y$10$HstBPIxnQKaLQjDRsi2QwuGDu11GXYwCoplJqmuvQqFrYCH692NcG', 2, 0, NULL),
(185, '<EMAIL>', '$2y$10$swoxuUi2umULIYFSxQect.qwtOE1H1O0.0b1wu8tXYHpeGDz0YFVC', 2, 0, NULL),
(186, '<EMAIL>', '$2y$10$qUIyCyj6PlOORqMG8RSp4uo/5DYO1bYbCGhxjg6eBRFHquXJehm9a', 4, 0, NULL),
(187, '<EMAIL>', '$2y$10$9dzAZqvvMga8SSpjwiE6ge0kpKcCvMbK6W3PMxPo.WSqPzeyGB3py', 4, 0, NULL),
(188, '<EMAIL>', '$2y$10$RrzcppGWUhzT0t1y9IIfR.KLM.QGZmA4N.EkBR2k1uFOWjg8N7BNC', 4, 0, NULL),
(189, '<EMAIL>', '$2y$10$1bSz8XwiKxuWgM0c46F76OqfzUsIKVLFkIgY1IUgs/60uKmrS2Vdu', 4, 0, NULL),
(190, '<EMAIL>', '$2y$10$j4LAv3Vb8XSryD5M.uSyxOfNRCjenS2wy9WnLrgvIAncy8WOzM8AG', 3, 0, NULL),
(191, '<EMAIL>', '$2y$10$1bmdDR5Pptz3O0Q6o4ewMujwcdmn1elTal6hVg2LKXV6V19eB3dlG', 3, 0, NULL),
(192, '<EMAIL>', '$2y$10$4iu280/MP.zEhYVONNrARuy3Jem3dtv3Ge2xqXrHVdiQHYuohe6Z6', 3, 0, NULL),
(193, '<EMAIL>', '$2y$10$D3ad3xYrZ7H8mMhQ6VWfe.topBAWmkUzkjhSviEzn.NnVwGNoRwAa', 3, 0, NULL),
(194, '<EMAIL>', '$2y$10$MYuuIUGL5TI.jhi035sREuozoZsvccEP6jnrLj.XaACZ2YYSI9nEu', 4, 0, NULL),
(196, '<EMAIL>', '$2y$10$trJSjYQseBOP/kGzBzLA..xmhM4FYJ73IqnqzbopGrUfghgprJ7eq', 2, 0, NULL),
(197, '<EMAIL>', '$2y$10$L/T0HR1jODR9e.wn66Mrt.NBhckmQACr3j.CPLt97CEuh4VIHAiLm', 4, 0, NULL),
(198, '<EMAIL>', '$2y$10$91ImddJL74m56jbhcNHUO.wKUczRyDPLtPW4RIJxY9Mv7xytHLxgG', 2, 0, NULL),
(200, '<EMAIL>', '$2y$10$LGxPzfVHeFijuwUJP3YwG.xOI307XnYV2.XRlAzrnOlbLvJ3C879e', 2, 0, NULL),
(202, '<EMAIL>', '$2y$10$R0mIngon4ItNAoWAnYBxc.ADFV3gnJICVgnlOTL95pmkNQylcnPSi', 3, 0, NULL);

--
-- Indexes for dumped tables
--

--
-- Indexes for table `admins`
--
ALTER TABLE `admins`
  ADD PRIMARY KEY (`admin_id`),
  ADD UNIQUE KEY `user_id` (`user_id`);

--
-- Indexes for table `admin_logs`
--
ALTER TABLE `admin_logs`
  ADD PRIMARY KEY (`log_id`),
  ADD KEY `idx_admin_id` (`admin_id`),
  ADD KEY `idx_action` (`action`),
  ADD KEY `idx_target_type` (`target_type`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `assessment`
--
ALTER TABLE `assessment`
  ADD PRIMARY KEY (`assessment_id`),
  ADD KEY `idx_subject_id` (`subject_id`),
  ADD KEY `idx_classroom_id` (`classroom_id`),
  ADD KEY `idx_assessment_date` (`assessment_date`);

--
-- Indexes for table `assessment_result`
--
ALTER TABLE `assessment_result`
  ADD PRIMARY KEY (`result_id`),
  ADD UNIQUE KEY `unique_student_assessment` (`student_id`,`assessment_id`),
  ADD KEY `idx_student_id` (`student_id`),
  ADD KEY `idx_assessment_id` (`assessment_id`),
  ADD KEY `idx_marks` (`marks`);

--
-- Indexes for table `attendance`
--
ALTER TABLE `attendance`
  ADD PRIMARY KEY (`attendance_id`),
  ADD KEY `student_id` (`student_id`),
  ADD KEY `teacher_id` (`teacher_id`),
  ADD KEY `classroom_id` (`classroom_id`);

--
-- Indexes for table `classrooms`
--
ALTER TABLE `classrooms`
  ADD PRIMARY KEY (`classroom_id`),
  ADD UNIQUE KEY `class_name` (`class_name`),
  ADD KEY `teacher_id` (`teacher_id`);

--
-- Indexes for table `discipline_incidents`
--
ALTER TABLE `discipline_incidents`
  ADD PRIMARY KEY (`incident_id`),
  ADD KEY `idx_student_id` (`student_id`),
  ADD KEY `idx_incident_date` (`incident_date`),
  ADD KEY `idx_teacher_id` (`teacher_id`);

--
-- Indexes for table `parents`
--
ALTER TABLE `parents`
  ADD PRIMARY KEY (`parent_id`),
  ADD UNIQUE KEY `user_id` (`user_id`);

--
-- Indexes for table `roles`
--
ALTER TABLE `roles`
  ADD PRIMARY KEY (`role_id`),
  ADD UNIQUE KEY `role_name` (`role_name`);

--
-- Indexes for table `schedules`
--
ALTER TABLE `schedules`
  ADD PRIMARY KEY (`schedule_id`),
  ADD KEY `idx_teacher_id` (`teacher_id`),
  ADD KEY `idx_classroom_id` (`classroom_id`),
  ADD KEY `idx_day_time` (`day_name`,`time_slot`);

--
-- Indexes for table `students`
--
ALTER TABLE `students`
  ADD PRIMARY KEY (`student_id`),
  ADD UNIQUE KEY `user_id` (`user_id`),
  ADD KEY `parent_id` (`parent_id`),
  ADD KEY `classroom_id` (`classroom_id`);

--
-- Indexes for table `student_subjects`
--
ALTER TABLE `student_subjects`
  ADD PRIMARY KEY (`student_id`,`subject_id`,`classroom_id`),
  ADD KEY `subject_id` (`subject_id`),
  ADD KEY `classroom_id` (`classroom_id`);

--
-- Indexes for table `subjects`
--
ALTER TABLE `subjects`
  ADD PRIMARY KEY (`subject_id`),
  ADD UNIQUE KEY `subject_name` (`subject_name`);

--
-- Indexes for table `teachers`
--
ALTER TABLE `teachers`
  ADD PRIMARY KEY (`teacher_id`),
  ADD UNIQUE KEY `user_id` (`user_id`),
  ADD UNIQUE KEY `staff_id` (`staff_id`);

--
-- Indexes for table `teacher_subject_classrooms`
--
ALTER TABLE `teacher_subject_classrooms`
  ADD PRIMARY KEY (`teacher_id`,`subject_id`,`classroom_id`),
  ADD UNIQUE KEY `subject_id` (`subject_id`,`classroom_id`),
  ADD KEY `classroom_id` (`classroom_id`);

--
-- Indexes for table `time_slots`
--
ALTER TABLE `time_slots`
  ADD PRIMARY KEY (`slot_id`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`user_id`),
  ADD UNIQUE KEY `email` (`email`),
  ADD KEY `role_id` (`role_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `admins`
--
ALTER TABLE `admins`
  MODIFY `admin_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `admin_logs`
--
ALTER TABLE `admin_logs`
  MODIFY `log_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=58;

--
-- AUTO_INCREMENT for table `assessment`
--
ALTER TABLE `assessment`
  MODIFY `assessment_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=44;

--
-- AUTO_INCREMENT for table `assessment_result`
--
ALTER TABLE `assessment_result`
  MODIFY `result_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=85;

--
-- AUTO_INCREMENT for table `attendance`
--
ALTER TABLE `attendance`
  MODIFY `attendance_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3797;

--
-- AUTO_INCREMENT for table `classrooms`
--
ALTER TABLE `classrooms`
  MODIFY `classroom_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=31;

--
-- AUTO_INCREMENT for table `discipline_incidents`
--
ALTER TABLE `discipline_incidents`
  MODIFY `incident_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `parents`
--
ALTER TABLE `parents`
  MODIFY `parent_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=71;

--
-- AUTO_INCREMENT for table `roles`
--
ALTER TABLE `roles`
  MODIFY `role_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `schedules`
--
ALTER TABLE `schedules`
  MODIFY `schedule_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=173;

--
-- AUTO_INCREMENT for table `students`
--
ALTER TABLE `students`
  MODIFY `student_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=60;

--
-- AUTO_INCREMENT for table `subjects`
--
ALTER TABLE `subjects`
  MODIFY `subject_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=27;

--
-- AUTO_INCREMENT for table `teachers`
--
ALTER TABLE `teachers`
  MODIFY `teacher_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=21;

--
-- AUTO_INCREMENT for table `time_slots`
--
ALTER TABLE `time_slots`
  MODIFY `slot_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=12;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `user_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=203;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `admins`
--
ALTER TABLE `admins`
  ADD CONSTRAINT `admins_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`);

--
-- Constraints for table `attendance`
--
ALTER TABLE `attendance`
  ADD CONSTRAINT `attendance_ibfk_1` FOREIGN KEY (`student_id`) REFERENCES `students` (`student_id`),
  ADD CONSTRAINT `attendance_ibfk_2` FOREIGN KEY (`teacher_id`) REFERENCES `teachers` (`teacher_id`),
  ADD CONSTRAINT `attendance_ibfk_3` FOREIGN KEY (`classroom_id`) REFERENCES `classrooms` (`classroom_id`);

--
-- Constraints for table `classrooms`
--
ALTER TABLE `classrooms`
  ADD CONSTRAINT `classrooms_ibfk_1` FOREIGN KEY (`teacher_id`) REFERENCES `teachers` (`teacher_id`);

--
-- Constraints for table `discipline_incidents`
--
ALTER TABLE `discipline_incidents`
  ADD CONSTRAINT `fk_discipline_student` FOREIGN KEY (`student_id`) REFERENCES `students` (`student_id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `parents`
--
ALTER TABLE `parents`
  ADD CONSTRAINT `parents_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`);

--
-- Constraints for table `students`
--
ALTER TABLE `students`
  ADD CONSTRAINT `students_ibfk_1` FOREIGN KEY (`parent_id`) REFERENCES `parents` (`parent_id`),
  ADD CONSTRAINT `students_ibfk_2` FOREIGN KEY (`classroom_id`) REFERENCES `classrooms` (`classroom_id`),
  ADD CONSTRAINT `students_ibfk_3` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`);

--
-- Constraints for table `student_subjects`
--
ALTER TABLE `student_subjects`
  ADD CONSTRAINT `student_subjects_ibfk_1` FOREIGN KEY (`student_id`) REFERENCES `students` (`student_id`),
  ADD CONSTRAINT `student_subjects_ibfk_2` FOREIGN KEY (`subject_id`) REFERENCES `subjects` (`subject_id`),
  ADD CONSTRAINT `student_subjects_ibfk_3` FOREIGN KEY (`classroom_id`) REFERENCES `classrooms` (`classroom_id`);

--
-- Constraints for table `teachers`
--
ALTER TABLE `teachers`
  ADD CONSTRAINT `teachers_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`);

--
-- Constraints for table `teacher_subject_classrooms`
--
ALTER TABLE `teacher_subject_classrooms`
  ADD CONSTRAINT `teacher_subject_classrooms_ibfk_1` FOREIGN KEY (`teacher_id`) REFERENCES `teachers` (`teacher_id`),
  ADD CONSTRAINT `teacher_subject_classrooms_ibfk_2` FOREIGN KEY (`subject_id`) REFERENCES `subjects` (`subject_id`),
  ADD CONSTRAINT `teacher_subject_classrooms_ibfk_3` FOREIGN KEY (`classroom_id`) REFERENCES `classrooms` (`classroom_id`);

--
-- Constraints for table `users`
--
ALTER TABLE `users`
  ADD CONSTRAINT `users_ibfk_1` FOREIGN KEY (`role_id`) REFERENCES `roles` (`role_id`);
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
