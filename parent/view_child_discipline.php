<?php
session_start();

if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'Parent') {
    header("Location: ../login.php");
    exit;
}

require '../db.php';

$userId = $_SESSION['user_id'];

// Get parent ID
$stmt = $conn->prepare("SELECT parent_id FROM parents WHERE user_id = ?");
$stmt->bind_param("i", $userId);
$stmt->execute();
$result = $stmt->get_result();
$parent = $result->fetch_assoc();

if (!$parent) {
    die("Maklumat ibu bapa tidak dijumpai.");
}

$parentId = $parent['parent_id'];

// Get selected child
$selectedChild = $_GET['student_id'] ?? '';

// Get children
$children = [];
$childStmt = $conn->prepare("
    SELECT student_id, full_name, no_ic, classroom_id
    FROM students
    WHERE parent_id = ?
    ORDER BY full_name
");
$childStmt->bind_param("i", $parentId);
$childStmt->execute();
$childResult = $childStmt->get_result();

while ($child = $childResult->fetch_assoc()) {
    $children[] = $child;
}

// Check if discipline_incidents table exists
$tableExists = false;
try {
    $checkTable = $conn->query("SHOW TABLES LIKE 'discipline_incidents'");
    if ($checkTable && $checkTable->num_rows > 0) {
        $tableExists = true;
    }
} catch (Exception $e) {
    $tableExists = false;
}

$incidents = [];
$stats = ['total' => 0, 'ringan' => 0, 'sederhana' => 0, 'berat' => 0];
$selectedChildInfo = null;

if ($tableExists && $selectedChild) {
    // Get selected child info
    foreach ($children as $child) {
        if ($child['student_id'] == $selectedChild) {
            $selectedChildInfo = $child;
            break;
        }
    }

    if ($selectedChildInfo) {
        try {
            // Get child's disciplinary incidents
            $incidentsStmt = $conn->prepare("
                SELECT di.*, t.full_name as teacher_name
                FROM discipline_incidents di
                JOIN teachers t ON di.teacher_id = t.teacher_id
                WHERE di.student_id = ?
                ORDER BY di.incident_date DESC, di.created_at DESC
            ");
            $incidentsStmt->bind_param("i", $selectedChild);
            $incidentsStmt->execute();
            $incidentsResult = $incidentsStmt->get_result();

            while ($incident = $incidentsResult->fetch_assoc()) {
                $incidents[] = $incident;
                $stats['total']++;
                $stats[strtolower($incident['severity'])]++;
            }
        } catch (Exception $e) {
            // Handle any database errors gracefully
            $incidents = [];
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ms">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rekod Disiplin Anak - SMKTMI</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>

<style>
/* Content Styling */
.content {
    margin-left: 280px;
    padding: 40px 30px;
    min-height: 100vh;
    background-color: #f9f9f9;
}

.form-container {
    width: 100%;
    max-width: 1200px;
    background-color: #fff;
    padding: 30px 40px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 1.1rem;
    margin: 0 auto;
}

.form-container h2 {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 25px;
    border-bottom: 3px solid #3498db;
    padding-bottom: 10px;
    color: #2980b9;
    text-align: center;
}

.filter-section {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 30px;
}

.stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stats-card {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    text-align: center;
    border-left: 4px solid #3498db;
}

.stats-card.total {
    border-left-color: #3498db;
}

.stats-card.ringan {
    border-left-color: #27ae60;
}

.stats-card.sederhana {
    border-left-color: #f39c12;
}

.stats-card.berat {
    border-left-color: #e74c3c;
}

.stats-number {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.stats-label {
    color: #7f8c8d;
    font-size: 0.9rem;
}

.incident-table th {
    background-color: rgb(153, 156, 158);
    color: white;
    text-align: center;
    font-weight: 600;
    padding: 12px;
    border: none;
}

.incident-table td {
    padding: 12px;
    text-align: center;
    vertical-align: middle;
    border-bottom: 1px solid #e9ecef;
}

.severity-ringan { background-color: #d4edda; color: #155724; }
.severity-sederhana { background-color: #fff3cd; color: #856404; }
.severity-berat { background-color: #f8d7da; color: #721c24; }

.status-baru { background-color: #cce5ff; color: #004085; }
.status-dalam-tindakan { background-color: #fff3cd; color: #856404; }
.status-selesai { background-color: #d4edda; color: #155724; }

.badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
}

.no-incidents {
    text-align: center;
    padding: 60px 20px;
    color: #7f8c8d;
}

.no-incidents i {
    font-size: 64px;
    margin-bottom: 20px;
    color: #bdc3c7;
}

/* Responsive */
@media (max-width: 768px) {
    .content {
        margin-left: 0;
        padding: 20px;
    }
    
    .form-container {
        padding: 20px;
    }
    
    .stats-cards {
        grid-template-columns: 1fr;
    }
}
</style>

<?php include 'includes/sidebar.php'; ?>

<div class="content">
  <div class="form-container">
    <h2><i class="fas fa-user-shield"></i> Rekod Disiplin Anak</h2>

    <div class="alert alert-info">
      <i class="fas fa-info-circle"></i> <strong>Maklumat:</strong> Rekod disiplin di bawah adalah laporan yang dibuat oleh guru-guru sekolah. Semua laporan akan kelihatan secara automatik di sini untuk membantu anda memantau perkembangan anak.
    </div>

    <!-- Filter Section -->
    <div class="filter-section">
      <h5 style="margin-bottom: 15px; color: #2c3e50;"><i class="fas fa-filter"></i> Pilih Anak</h5>
      <form method="get" class="row g-3">
        <div class="col-md-8">
          <label for="student_id" class="form-label">Pilih Anak:</label>
          <select name="student_id" id="student_id" class="form-select" onchange="this.form.submit()">
            <option value="">-- Pilih Anak --</option>
            <?php foreach ($children as $child): ?>
              <option value="<?php echo $child['student_id']; ?>" 
                      <?php echo ($selectedChild == $child['student_id']) ? 'selected' : ''; ?>>
                <?php echo htmlspecialchars($child['full_name']); ?> (<?php echo htmlspecialchars($child['no_ic']); ?>)
              </option>
            <?php endforeach; ?>
          </select>
        </div>
      </form>
    </div>

    <?php if ($tableExists): ?>
      <?php if ($selectedChild && $selectedChildInfo): ?>
        <!-- Child Info -->
        <div class="alert alert-info">
          <h6 class="mb-2"><i class="fas fa-user-graduate"></i> Maklumat Anak</h6>
          <strong>Nama:</strong> <?php echo htmlspecialchars($selectedChildInfo['full_name']); ?> | 
          <strong>No. IC:</strong> <?php echo htmlspecialchars($selectedChildInfo['no_ic']); ?>
        </div>

        <!-- Statistics Cards -->
        <div class="stats-cards">
          <div class="stats-card total">
            <div class="stats-number" style="color: #3498db;"><?php echo $stats['total']; ?></div>
            <div class="stats-label">Jumlah Laporan</div>
          </div>
          <div class="stats-card ringan">
            <div class="stats-number" style="color: #27ae60;"><?php echo $stats['ringan']; ?></div>
            <div class="stats-label">Kesalahan Ringan</div>
          </div>
          <div class="stats-card sederhana">
            <div class="stats-number" style="color: #f39c12;"><?php echo $stats['sederhana']; ?></div>
            <div class="stats-label">Kesalahan Sederhana</div>
          </div>
          <div class="stats-card berat">
            <div class="stats-number" style="color: #e74c3c;"><?php echo $stats['berat']; ?></div>
            <div class="stats-label">Kesalahan Berat</div>
          </div>
        </div>

        <?php if (!empty($incidents)): ?>
          <!-- Incidents Table -->
          <h5 style="margin-bottom: 20px; color: #2c3e50;"><i class="fas fa-list"></i> Senarai Laporan Disiplin</h5>
          <div class="table-responsive">
            <table class="table table-bordered incident-table">
              <thead>
                <tr>
                  <th>Bil.</th>
                  <th>Tarikh</th>
                  <th>Jenis Kesalahan</th>
                  <th>Penerangan</th>
                  <th>Tahap</th>
                  <th>Status</th>
                  <th>Guru Pelapor</th>
                  <th>Tindakan Diambil</th>
                </tr>
              </thead>
              <tbody>
                <?php $bil = 1; foreach ($incidents as $incident): ?>
                  <tr>
                    <td><?php echo $bil++; ?></td>
                    <td><?php echo date('d/m/Y', strtotime($incident['incident_date'])); ?></td>
                    <td><?php echo htmlspecialchars($incident['incident_type']); ?></td>
                    <td style="text-align: left; max-width: 200px;">
                      <?php echo htmlspecialchars(substr($incident['description'], 0, 100)); ?>
                      <?php if (strlen($incident['description']) > 100): ?>...<?php endif; ?>
                    </td>
                    <td>
                      <span class="badge severity-<?php echo strtolower($incident['severity']); ?>">
                        <?php echo $incident['severity']; ?>
                      </span>
                    </td>
                    <td>
                      <span class="badge status-<?php echo strtolower(str_replace(' ', '-', $incident['status'])); ?>">
                        <?php echo $incident['status']; ?>
                      </span>
                    </td>
                    <td><?php echo htmlspecialchars($incident['teacher_name']); ?></td>
                    <td style="text-align: left; max-width: 150px;">
                      <?php if ($incident['action_taken']): ?>
                        <?php echo htmlspecialchars(substr($incident['action_taken'], 0, 80)); ?>
                        <?php if (strlen($incident['action_taken']) > 80): ?>...<?php endif; ?>
                      <?php else: ?>
                        <em style="color: #7f8c8d;">Tiada tindakan dicatat</em>
                      <?php endif; ?>
                    </td>
                  </tr>
                <?php endforeach; ?>
              </tbody>
            </table>
          </div>

          <!-- Parent Advice Section -->
          <div class="alert alert-warning mt-4">
            <h6><i class="fas fa-lightbulb"></i> Panduan untuk Ibu Bapa</h6>
            <ul class="mb-0">
              <li>Berbincang dengan anak mengenai tingkah laku yang tidak sesuai</li>
              <li>Bekerjasama dengan pihak sekolah untuk membantu anak</li>
              <li>Pantau perkembangan anak di rumah dan di sekolah</li>
              <li>Berikan sokongan dan bimbingan yang positif</li>
              <li>Hubungi guru kelas atau kaunselor sekolah jika diperlukan</li>
              <li><strong>Nota:</strong> Laporan ini dibuat secara langsung oleh guru dan akan kelihatan kepada anda dan anak secara automatik</li>
            </ul>
          </div>

        <?php else: ?>
          <div class="no-incidents">
            <i class="fas fa-medal"></i>
            <h4 style="color: #27ae60;">Tahniah! Rekod Disiplin Bersih</h4>
            <p>Anak anda tidak mempunyai sebarang laporan disiplin. Teruskan bimbingan yang baik!</p>
          </div>
        <?php endif; ?>

      <?php elseif (!empty($children)): ?>
        <div class="text-center" style="padding: 60px;">
          <i class="fas fa-user-check" style="font-size: 64px; color: #bdc3c7; margin-bottom: 20px;"></i>
          <h4 style="color: #7f8c8d;">Sila Pilih Anak</h4>
          <p style="color: #95a5a6;">Pilih anak dari senarai di atas untuk melihat rekod disiplin.</p>
        </div>
      <?php else: ?>
        <div class="text-center" style="padding: 60px;">
          <i class="fas fa-user-plus" style="font-size: 64px; color: #bdc3c7; margin-bottom: 20px;"></i>
          <h4 style="color: #7f8c8d;">Belum Ada Anak Didaftarkan</h4>
          <p style="color: #95a5a6;">Sila daftarkan anak anda terlebih dahulu.</p>
          <a href="daftar_anak.php" class="btn btn-primary">
            <i class="fas fa-plus"></i> Daftar Anak
          </a>
        </div>
      <?php endif; ?>

    <?php else: ?>
      <div class="alert alert-info">
        <h5><i class="fas fa-info-circle"></i> Maklumat</h5>
        <p class="mb-0">Sistem pengurusan disiplin belum diaktifkan. Sila hubungi pentadbir sistem untuk maklumat lanjut.</p>
      </div>
    <?php endif; ?>
  </div>
</div>

</body>
</html>
