<?php
session_start();
require '../db.php';
include 'includes/header.php'; // panggil header & sidebar
include 'includes/sidebar.php';

$teacher_user_id = $_SESSION['user_id'];
$message = "";

// Dapatkan teacher_id berdasarkan user_id
$stmt = $conn->prepare("SELECT teacher_id FROM teachers WHERE user_id = ?");
$stmt->bind_param("i", $teacher_user_id);
$stmt->execute();
$result = $stmt->get_result();
$teacher = $result->fetch_assoc();
$teacher_id = $teacher['teacher_id'] ?? 0;

// Dapatkan senarai subjek & kelas yang diajar oleh guru
$teaching = [];
$stmt2 = $conn->prepare("
    SELECT tsc.subject_id, tsc.classroom_id, s.subject_name, c.class_name
    FROM teacher_subject_classrooms tsc
    JOIN subjects s ON s.subject_id = tsc.subject_id
    JOIN classrooms c ON c.classroom_id = tsc.classroom_id
    WHERE tsc.teacher_id = ?
    ORDER BY c.tingkatan ASC, c.class_name ASC
");
$stmt2->bind_param("i", $teacher_id);
$stmt2->execute();
$res2 = $stmt2->get_result();
while ($row = $res2->fetch_assoc()) {
    $teaching[] = $row;
}

// Jika borang disubmit
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['subject_id'], $_POST['classroom_id'])) {
    $subject_id = (int)$_POST['subject_id'];
    $classroom_id = (int)$_POST['classroom_id'];
    $student_ids = $_POST['student_ids'] ?? [];

    // Padam semua pendaftaran pelajar dahulu
    $deleteStmt = $conn->prepare("DELETE FROM student_subjects WHERE subject_id = ? AND classroom_id = ?");
    $deleteStmt->bind_param("ii", $subject_id, $classroom_id);
    $deleteStmt->execute();

    // Daftar semula pelajar yang dipilih
    if (!empty($student_ids)) {
        $insertStmt = $conn->prepare("INSERT INTO student_subjects (student_id, subject_id, classroom_id) VALUES (?, ?, ?)");
        foreach ($student_ids as $sid) {
            $student_id = (int)$sid;
            $insertStmt->bind_param("iii", $student_id, $subject_id, $classroom_id);
            $insertStmt->execute();
        }
    }

    $message = "✅ Pendaftaran pelajar berjaya disimpan.";
}

// Untuk paparan senarai pelajar bila subjek dan kelas dipilih
$selected_subject_id = $_GET['subject_id'] ?? null;
$selected_classroom_id = $_GET['classroom_id'] ?? null;
$students = [];

if ($selected_subject_id && $selected_classroom_id) {
    $stmt3 = $conn->prepare("
        SELECT s.student_id, s.full_name,
        EXISTS (
            SELECT 1 FROM student_subjects ss
            WHERE ss.student_id = s.student_id AND ss.subject_id = ? AND ss.classroom_id = ?
        ) AS is_selected
        FROM students s
        WHERE s.classroom_id = ?
        ORDER BY s.full_name ASC
    ");
    $stmt3->bind_param("iii", $selected_subject_id, $selected_classroom_id, $selected_classroom_id);
    $stmt3->execute();
    $students = $stmt3->get_result()->fetch_all(MYSQLI_ASSOC);
}
?>

<!DOCTYPE html>
<html lang="ms">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Daftar Pelajar-Subjek - SMKTMI</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>

<style>
/* Content Styling */
.content {
    margin-left: 280px;
    padding: 40px 30px;
    min-height: 100vh;
    background-color: #f9f9f9;
}

.form-container {
    width: 100%;
    max-width: 1200px;
    background-color: #fff;
    padding: 30px 40px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0 auto;
}

.form-container h2 {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 25px;
    border-bottom: 3px solid #3498db;
    padding-bottom: 10px;
    color: #2980b9;
    text-align: center;
}

.alert {
    padding: 15px;
    background-color: #d9edf7;
    border: 1px solid #bce8f1;
    border-radius: 5px;
    color: #31708f;
    margin-bottom: 25px;
    text-align: center;
}

.alert-success {
    background-color: #dff0d8;
    border-color: #d6e9c6;
    color: #3c763d;
}

.filter-section {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 30px;
    border-left: 4px solid #3498db;
}

.stats-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border-left: 4px solid #3498db;
    transition: transform 0.2s ease;
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.btn-primary {
    background-color: #3498db;
    border-color: #3498db;
    padding: 10px 30px;
    font-weight: 600;
}

.btn-primary:hover {
    background-color: #2980b9;
    border-color: #2980b9;
}

/* Responsive */
@media (max-width: 768px) {
    .content {
        margin-left: 0;
        padding: 20px;
    }
}
</style>

<?php include 'includes/sidebar.php'; ?>

<div class="content">
  <div class="form-container">
    <h2><i class="fas fa-book-open"></i> Daftar Pelajar-Subjek</h2>

    <?php if (!empty($message)): ?>
      <div class="alert alert-success">
        <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($message); ?>
      </div>
    <?php endif; ?>

    <!-- Subject & Class Selection -->
    <div class="filter-section">
      <h5 style="margin-bottom: 15px; color: #2c3e50;">
        <i class="fas fa-filter"></i> Pilih Subjek & Kelas
      </h5>
      <form method="get">
        <div class="row align-items-end">
          <div class="col-md-8">
            <label for="select_subclass" class="form-label"><strong>Subjek & Kelas:</strong></label>
            <select id="select_subclass" name="subject_id_classroom" class="form-select" onchange="this.form.submit()">
              <option value="">-- Pilih Subjek & Kelas --</option>
              <?php foreach ($teaching as $item):
                  $val = $item['subject_id'] . '_' . $item['classroom_id'];
                  $selected = ($item['subject_id'] == $selected_subject_id && $item['classroom_id'] == $selected_classroom_id) ? 'selected' : '';
              ?>
                <option value="<?php echo $val; ?>" <?php echo $selected; ?>>
                  <?php echo htmlspecialchars($item['subject_name'] . ' - ' . $item['class_name']); ?>
                </option>
              <?php endforeach; ?>
            </select>
          </div>
          <div class="col-md-4">
            <?php if ($selected_subject_id && $selected_classroom_id): ?>
              <span class="badge bg-success" style="font-size: 14px; padding: 8px 12px;">
                <i class="fas fa-check-circle"></i> Subjek & Kelas Dipilih
              </span>
            <?php endif; ?>
          </div>
        </div>
      </form>
    </div>

    <?php if ($selected_subject_id && $selected_classroom_id): ?>
      <div class="stats-card">
        <h5 style="margin-bottom: 20px; color: #2c3e50; text-align: center;">
          <i class="fas fa-users"></i> Senarai Pelajar
        </h5>

        <form method="post">
          <input type="hidden" name="subject_id" value="<?php echo htmlspecialchars($selected_subject_id); ?>">
          <input type="hidden" name="classroom_id" value="<?php echo htmlspecialchars($selected_classroom_id); ?>">

          <div style="max-height: 400px; overflow-y: auto;">
            <table class="table table-bordered">
              <thead class="table-light">
                <tr>
                  <th width="60">Bil.</th>
                  <th>Nama Pelajar</th>
                  <th width="80" class="text-center">Pilih</th>
                </tr>
              </thead>
              <tbody>
                <?php foreach ($students as $index => $student): ?>
                  <tr>
                    <td class="text-center"><?php echo $index + 1; ?></td>
                    <td><?php echo htmlspecialchars($student['full_name']); ?></td>
                    <td class="text-center">
                      <input type="checkbox" class="form-check-input" name="student_ids[]"
                             value="<?php echo $student['student_id']; ?>"
                             <?php echo $student['is_selected'] ? 'checked' : ''; ?>>
                    </td>
                  </tr>
                <?php endforeach; ?>
              </tbody>
            </table>
          </div>

          <div class="text-center mt-3">
            <button type="submit" class="btn btn-primary">
              <i class="fas fa-save"></i> Simpan Pendaftaran
            </button>
          </div>

          <div class="text-center mt-2">
            <small class="text-muted">
              <i class="fas fa-info-circle"></i>
              Tandakan kotak untuk mendaftarkan pelajar ke dalam subjek ini
            </small>
          </div>
        </form>
      </div>
    <?php else: ?>
      <div class="stats-card" style="text-align: center; padding: 40px;">
        <i class="fas fa-book-open" style="font-size: 64px; color: #bdc3c7; margin-bottom: 20px;"></i>
        <h4 style="color: #7f8c8d; margin-bottom: 15px;">Pilih Subjek & Kelas</h4>
        <p style="color: #95a5a6;">
          Sila pilih subjek dan kelas dari dropdown di atas untuk melihat senarai pelajar.
        </p>
      </div>
    <?php endif; ?>
  </div>
</div>

<script>
// Parsing parameter subject_id_classroom=1_3
document.addEventListener('DOMContentLoaded', function () {
    const urlParams = new URLSearchParams(window.location.search);
    const pair = urlParams.get('subject_id_classroom');
    if (pair) {
        const parts = pair.split('_');
        if (parts.length === 2) {
            window.location.href = `?subject_id=${parts[0]}&classroom_id=${parts[1]}`;
        }
    }
});
</script>
</body>
</html>
