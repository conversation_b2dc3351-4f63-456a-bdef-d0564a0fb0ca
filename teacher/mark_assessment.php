<?php
session_start();

// Check if user is logged in and is teacher
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'Teacher') {
    header("Location: ../login.php");
    exit;
}

require '../db.php';
require '../includes/universal_logger.php';

$message = "";
$teacher_id = $_SESSION['teacher_id'] ?? null;

// Get teacher ID if not in session
if (!$teacher_id) {
    $stmt = $conn->prepare("SELECT teacher_id FROM teachers WHERE user_id = ?");
    $stmt->bind_param("i", $_SESSION['user_id']);
    $stmt->execute();
    $result = $stmt->get_result();
    if ($row = $result->fetch_assoc()) {
        $teacher_id = $row['teacher_id'];
        $_SESSION['teacher_id'] = $teacher_id;
    }
    $stmt->close();
}

if (!$teacher_id) {
    die("Teacher information not found.");
}

// Function to calculate grade based on marks
function calculateGrade($marks) {
    if ($marks >= 90) return ['grade' => 'A+', 'description' => 'Cemerlang Tertinggi'];
    if ($marks >= 80) return ['grade' => 'A', 'description' => 'Cemerlang Tinggi'];
    if ($marks >= 70) return ['grade' => 'A-', 'description' => 'Cemerlang'];
    if ($marks >= 65) return ['grade' => 'B+', 'description' => 'Kepujian Tertinggi'];
    if ($marks >= 60) return ['grade' => 'B', 'description' => 'Kepujian Tinggi'];
    if ($marks >= 55) return ['grade' => 'C+', 'description' => 'Kepujian Atas'];
    if ($marks >= 50) return ['grade' => 'C', 'description' => 'Kepujian'];
    if ($marks >= 45) return ['grade' => 'D', 'description' => 'Lulus Atas'];
    if ($marks >= 40) return ['grade' => 'E', 'description' => 'Lulus'];
    return ['grade' => 'G', 'description' => 'Gagal'];
}

// Function to get grade color
function getGradeColor($grade) {
    switch ($grade) {
        case 'A+': case 'A': case 'A-': return '#27ae60'; // Green
        case 'B+': case 'B': return '#3498db'; // Blue
        case 'C+': case 'C': return '#f39c12'; // Orange
        case 'D': case 'E': return '#e67e22'; // Dark Orange
        case 'G': return '#e74c3c'; // Red
        default: return '#95a5a6'; // Gray
    }
}

// Handle marks submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['marks']) && isset($_POST['assessment_id'])) {
    $assessment_id = (int)$_POST['assessment_id'];
    $marks_data = $_POST['marks'];
    $success_count = 0;
    $error_count = 0;

    foreach ($marks_data as $student_id => $marks) {
        $student_id = (int)$student_id;
        $marks = (float)$marks;

        // Convert to integer (whole number only)
        $marks = (int)$marks;

        if ($marks >= 0 && $marks <= 100) {
            // Check if result already exists
            $check_stmt = $conn->prepare("SELECT result_id FROM assessment_result WHERE assessment_id = ? AND student_id = ?");
            $check_stmt->bind_param("ii", $assessment_id, $student_id);
            $check_stmt->execute();
            $check_result = $check_stmt->get_result();

            if ($check_result->num_rows > 0) {
                // Update existing result
                $update_stmt = $conn->prepare("UPDATE assessment_result SET marks = ? WHERE assessment_id = ? AND student_id = ?");
                $update_stmt->bind_param("iii", $marks, $assessment_id, $student_id);
                if ($update_stmt->execute()) {
                    $success_count++;
                } else {
                    $error_count++;
                }
                $update_stmt->close();
            } else {
                // Insert new result
                $insert_stmt = $conn->prepare("INSERT INTO assessment_result (assessment_id, student_id, marks) VALUES (?, ?, ?)");
                $insert_stmt->bind_param("iii", $assessment_id, $student_id, $marks);
                if ($insert_stmt->execute()) {
                    $success_count++;
                } else {
                    $error_count++;
                }
                $insert_stmt->close();
            }
            $check_stmt->close();
        }
    }

    if ($success_count > 0) {
        // Log the assessment marking with details
        $assessment_info_stmt = $conn->prepare("
            SELECT a.assessment_type, s.subject_name, c.class_name
            FROM assessment a
            JOIN subjects s ON a.subject_id = s.subject_id
            JOIN classrooms c ON a.classroom_id = c.classroom_id
            WHERE a.assessment_id = ?
        ");
        $assessment_info_stmt->bind_param("i", $assessment_id);
        $assessment_info_stmt->execute();
        $assessment_info_result = $assessment_info_stmt->get_result();
        if ($assessment_info = $assessment_info_result->fetch_assoc()) {
            $details = [
                'Jenis Penilaian' => $assessment_info['assessment_type'],
                'Subjek' => $assessment_info['subject_name'],
                'Kelas' => $assessment_info['class_name'],
                'Pelajar Berjaya' => $success_count,
                'Pelajar Gagal' => $error_count
            ];

            logUserActivity($conn, 'MARK', 'ASSESSMENT', $assessment_id,
                $assessment_info['assessment_type'],
                "Markah dimasukkan untuk penilaian", $details);
        }
        $assessment_info_stmt->close();

        $message = "✅ $success_count markah berjaya disimpan.";
        if ($error_count > 0) {
            $message .= " $error_count markah gagal disimpan.";
        }
    } else {
        $message = "❌ Tiada markah yang disimpan.";
    }
}

// Get assessments assigned to this teacher
$assessments = [];

// Check if assessment tables exist
$table_check = $conn->query("SHOW TABLES LIKE 'assessment'");
if ($table_check && $table_check->num_rows > 0) {
    try {
        $assessment_sql = "
            SELECT DISTINCT a.assessment_id, a.assessment_type, a.assessment_date,
                   s.subject_name, c.class_name, c.classroom_id,
                   (SELECT COUNT(*) FROM assessment_result ar WHERE ar.assessment_id = a.assessment_id) as marked_count,
                   (SELECT COUNT(*) FROM students st WHERE st.classroom_id = c.classroom_id) as total_students
            FROM assessment a
            JOIN subjects s ON a.subject_id = s.subject_id
            JOIN classrooms c ON a.classroom_id = c.classroom_id
            JOIN teacher_subject_classrooms tsc ON (tsc.subject_id = a.subject_id AND tsc.classroom_id = a.classroom_id)
            WHERE tsc.teacher_id = ?
            ORDER BY a.assessment_date DESC, a.assessment_id DESC
        ";

        $stmt = $conn->prepare($assessment_sql);
        $stmt->bind_param("i", $teacher_id);
        $stmt->execute();
        $result = $stmt->get_result();
        while ($row = $result->fetch_assoc()) {
            $assessments[] = $row;
        }
        $stmt->close();
    } catch (mysqli_sql_exception $e) {
        // Assessment tables don't exist or have issues
        $assessments = [];
    }
}

// Get specific assessment details if viewing or editing one
$selected_assessment = null;
$students = [];
$view_mode = false;

if ((isset($_GET['assessment_id']) && is_numeric($_GET['assessment_id'])) ||
    (isset($_GET['view']) && is_numeric($_GET['view']))) {

    $assessment_id = isset($_GET['assessment_id']) ? (int)$_GET['assessment_id'] : (int)$_GET['view'];
    $view_mode = isset($_GET['view']);

    // Verify teacher has access to this assessment
    $verify_stmt = $conn->prepare("
        SELECT a.*, s.subject_name, c.class_name, c.classroom_id
        FROM assessment a
        JOIN subjects s ON a.subject_id = s.subject_id
        JOIN classrooms c ON a.classroom_id = c.classroom_id
        JOIN teacher_subject_classrooms tsc ON (tsc.subject_id = a.subject_id AND tsc.classroom_id = a.classroom_id)
        WHERE a.assessment_id = ? AND tsc.teacher_id = ?
    ");
    $verify_stmt->bind_param("ii", $assessment_id, $teacher_id);
    $verify_stmt->execute();
    $verify_result = $verify_stmt->get_result();

    if ($verify_result->num_rows === 1) {
        $selected_assessment = $verify_result->fetch_assoc();

        // Get students in this classroom with their current marks
        $students_stmt = $conn->prepare("
            SELECT s.student_id, s.full_name, s.no_ic,
                   ar.marks
            FROM students s
            LEFT JOIN assessment_result ar ON s.student_id = ar.student_id AND ar.assessment_id = ?
            WHERE s.classroom_id = ?
            ORDER BY s.full_name
        ");
        $students_stmt->bind_param("ii", $assessment_id, $selected_assessment['classroom_id']);
        $students_stmt->execute();
        $students_result = $students_stmt->get_result();

        while ($row = $students_result->fetch_assoc()) {
            $students[] = $row;
        }
        $students_stmt->close();
    }
    $verify_stmt->close();
}
?>

<!DOCTYPE html>
<html lang="ms">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Markah Penilaian - SMKTMI</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>

<style>
/* Content Styling */
.content {
    margin-left: 280px;
    padding: 40px 30px;
    min-height: 100vh;
    background-color: #f9f9f9;
}

.form-container {
    width: 100%;
    max-width: 1200px;
    background-color: #fff;
    padding: 30px 40px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0 auto;
}

.form-container h2 {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 25px;
    border-bottom: 3px solid #3498db;
    padding-bottom: 10px;
    color: #2980b9;
    text-align: center;
}

.alert {
    padding: 15px;
    background-color: #d9edf7;
    border: 1px solid #bce8f1;
    border-radius: 5px;
    color: #31708f;
    margin-bottom: 25px;
    text-align: center;
}

.alert-success {
    background-color: #dff0d8;
    border-color: #d6e9c6;
    color: #3c763d;
}

.alert-danger {
    background-color: #f2dede;
    border-color: #ebccd1;
    color: #a94442;
}

.assessment-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border-left: 4px solid #3498db;
    transition: transform 0.2s ease;
}

.assessment-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.assessment-info {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 30px;
    border-left: 4px solid #3498db;
}

.badge {
    padding: 6px 10px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

.bg-success {
    background-color: #27ae60 !important;
    color: white;
}

.bg-warning {
    background-color: #f39c12 !important;
    color: white;
}

.bg-secondary {
    background-color: #95a5a6 !important;
    color: white;
}

.marks-input {
    width: 80px;
    padding: 5px 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-align: center;
}

.marks-input:focus {
    border-color: #3498db;
    outline: none;
}

.btn-primary {
    background-color: #3498db;
    border-color: #3498db;
    padding: 10px 30px;
    font-weight: 600;
}

.btn-primary:hover {
    background-color: #2980b9;
    border-color: #2980b9;
}

.btn-secondary {
    background-color: #95a5a6;
    border-color: #95a5a6;
    padding: 10px 20px;
}

.btn-secondary:hover {
    background-color: #7f8c8d;
    border-color: #7f8c8d;
}

.btn-mark {
    background-color: #27ae60;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    text-decoration: none;
    font-size: 14px;
    transition: background-color 0.3s ease;
}

.btn-mark:hover {
    background-color: #229954;
    color: white;
    text-decoration: none;
}

/* Responsive */
@media (max-width: 768px) {
    .sidebar {
        width: 100%;
        height: auto;
        position: relative;
        box-shadow: none;
    }
    .content {
        margin-left: 0;
        padding: 20px;
    }
}
</style>

<?php include 'includes/sidebar.php'; ?>

<div class="content">
  <div class="form-container">
    <h2>Markah Penilaian</h2>

    <?php if (!empty($message)): ?>
      <div class="alert <?php echo strpos($message, '✅') !== false ? 'alert-success' : 'alert-danger'; ?>">
        <?php echo htmlspecialchars($message); ?>
      </div>
    <?php endif; ?>

    <?php if (!$selected_assessment): ?>
      <!-- Assessment List View -->
      <h4 style="margin-bottom: 20px; color: #2c3e50;">
        <i class="fas fa-clipboard-list"></i> Senarai Penilaian Anda
      </h4>

      <?php if (count($assessments) > 0): ?>
        <?php foreach ($assessments as $assessment): ?>
          <div class="assessment-card">
            <div class="row align-items-center">
              <div class="col-md-8">
                <h5 style="margin-bottom: 10px; color: #2c3e50;">
                  <?php echo htmlspecialchars($assessment['assessment_type']); ?>
                </h5>
                <p style="margin-bottom: 5px; color: #7f8c8d;">
                  <i class="fas fa-calendar"></i>
                  <strong>Tarikh:</strong> <?php echo date('d/m/Y', strtotime($assessment['assessment_date'])); ?>
                </p>
                <p style="margin-bottom: 5px; color: #7f8c8d;">
                  <i class="fas fa-book"></i>
                  <strong>Subjek:</strong> <?php echo htmlspecialchars($assessment['subject_name']); ?>
                </p>
                <p style="margin-bottom: 0; color: #7f8c8d;">
                  <i class="fas fa-users"></i>
                  <strong>Kelas:</strong> <?php echo htmlspecialchars($assessment['class_name']); ?>
                </p>
              </div>
              <div class="col-md-4 text-end">
                <div style="margin-bottom: 15px;">
                  <?php
                  $completion_percentage = $assessment['total_students'] > 0 ?
                    round(($assessment['marked_count'] / $assessment['total_students']) * 100) : 0;
                  ?>

                  <?php if ($completion_percentage == 100): ?>
                    <span class="badge bg-success">
                      <i class="fas fa-check-circle"></i> Selesai (<?php echo $assessment['marked_count']; ?>/<?php echo $assessment['total_students']; ?>)
                    </span>
                  <?php elseif ($completion_percentage > 0): ?>
                    <span class="badge bg-warning">
                      <i class="fas fa-clock"></i> Sebahagian (<?php echo $assessment['marked_count']; ?>/<?php echo $assessment['total_students']; ?>)
                    </span>
                  <?php else: ?>
                    <span class="badge bg-secondary">
                      <i class="fas fa-hourglass-start"></i> Belum Mula (0/<?php echo $assessment['total_students']; ?>)
                    </span>
                  <?php endif; ?>
                </div>
                <div style="display: flex; gap: 10px; justify-content: flex-end;">
                  <a href="mark_assessment.php?assessment_id=<?php echo $assessment['assessment_id']; ?>"
                     class="btn-mark">
                    <i class="fas fa-pencil-alt"></i> Beri Markah
                  </a>
                  <a href="mark_assessment.php?view=<?php echo $assessment['assessment_id']; ?>"
                     class="btn btn-secondary" style="padding: 8px 16px; font-size: 14px;">
                    <i class="fas fa-eye"></i> Lihat
                  </a>
                </div>
              </div>
            </div>
          </div>
        <?php endforeach; ?>
      <?php else: ?>
        <div style="text-align: center; padding: 60px 20px;">
          <i class="fas fa-clipboard-list" style="font-size: 64px; color: #bdc3c7; margin-bottom: 20px;"></i>
          <h4 style="color: #7f8c8d; margin-bottom: 15px;">Tiada Penilaian Dijumpai</h4>
          <p style="color: #95a5a6;">
            Tiada penilaian yang ditetapkan untuk subjek dan kelas yang anda ajar.
            <br>Sila hubungi pentadbir untuk maklumat lanjut.
          </p>
        </div>
      <?php endif; ?>

    <?php else: ?>
      <!-- Marks Entry/View Mode -->
      <div class="assessment-info">
        <h4 style="margin-bottom: 15px; color: #2c3e50;">
          <i class="fas fa-clipboard-list"></i>
          <?php echo $view_mode ? 'Lihat Markah Penilaian' : 'Masukkan Markah Penilaian'; ?>
        </h4>
        <div class="row">
          <div class="col-md-6">
            <p><strong>Jenis Penilaian:</strong> <?php echo htmlspecialchars($selected_assessment['assessment_type']); ?></p>
            <p><strong>Subjek:</strong> <?php echo htmlspecialchars($selected_assessment['subject_name']); ?></p>
          </div>
          <div class="col-md-6">
            <p><strong>Tarikh:</strong> <?php echo date('d/m/Y', strtotime($selected_assessment['assessment_date'])); ?></p>
            <p><strong>Kelas:</strong> <?php echo htmlspecialchars($selected_assessment['class_name']); ?></p>
          </div>
        </div>
      </div>

      <?php if (count($students) > 0): ?>
        <?php if (!$view_mode): ?>
          <!-- Marks Entry Form -->
          <form method="post">
            <input type="hidden" name="assessment_id" value="<?php echo $selected_assessment['assessment_id']; ?>">
        <?php endif; ?>

        <table class="table table-bordered">
          <thead class="table-light">
            <tr>
              <th width="60">Bil.</th>
              <th>Nama Pelajar</th>
              <th>No. Kad Pengenalan</th>
              <th width="100">Markah</th>
              <th width="120">Gred</th>
              <th>Keterangan</th>
            </tr>
          </thead>
          <tbody>
            <?php foreach ($students as $index => $student): ?>
              <?php
              $gradeInfo = null;
              if ($student['marks'] !== null) {
                  $gradeInfo = calculateGrade($student['marks']);
              }
              ?>
              <tr>
                <td class="text-center"><?php echo $index + 1; ?></td>
                <td><?php echo htmlspecialchars($student['full_name']); ?></td>
                <td><?php echo htmlspecialchars($student['no_ic']); ?></td>
                <td class="text-center">
                  <?php if ($view_mode): ?>
                    <span style="font-weight: bold; color: #2c3e50;">
                      <?php echo $student['marks'] !== null ? (int)$student['marks'] : '-'; ?>
                    </span>
                  <?php else: ?>
                    <input type="number"
                           name="marks[<?php echo $student['student_id']; ?>]"
                           class="marks-input"
                           min="0"
                           max="100"
                           step="1"
                           value="<?php echo $student['marks'] !== null ? (int)$student['marks'] : ''; ?>"
                           placeholder="0"
                           onchange="updateGrade(this, <?php echo $index; ?>)">
                  <?php endif; ?>
                </td>
                <td class="text-center" id="grade-<?php echo $index; ?>">
                  <?php if ($gradeInfo): ?>
                    <span class="badge" style="background-color: <?php echo getGradeColor($gradeInfo['grade']); ?>; color: white; font-size: 14px; padding: 6px 10px;">
                      <?php echo $gradeInfo['grade']; ?>
                    </span>
                  <?php else: ?>
                    <span class="badge bg-secondary">-</span>
                  <?php endif; ?>
                </td>
                <td id="description-<?php echo $index; ?>">
                  <?php echo $gradeInfo ? $gradeInfo['description'] : '-'; ?>
                </td>
              </tr>
            <?php endforeach; ?>
          </tbody>
        </table>

        <?php if (!$view_mode): ?>
          <div style="text-align: center; margin-top: 30px;">
            <button type="submit" class="btn btn-primary">
              <i class="fas fa-save"></i> Simpan Markah
            </button>
            <a href="mark_assessment.php" class="btn btn-secondary" style="margin-left: 15px;">
              <i class="fas fa-arrow-left"></i> Kembali
            </a>
          </div>
          </form>
        <?php else: ?>
          <div style="text-align: center; margin-top: 30px;">
            <a href="mark_assessment.php?assessment_id=<?php echo $selected_assessment['assessment_id']; ?>" class="btn btn-primary">
              <i class="fas fa-edit"></i> Edit Markah
            </a>
            <a href="mark_assessment.php" class="btn btn-secondary" style="margin-left: 15px;">
              <i class="fas fa-arrow-left"></i> Kembali
            </a>
          </div>
        <?php endif; ?>
      <?php else: ?>
        <div style="text-align: center; padding: 40px;">
          <i class="fas fa-users" style="font-size: 48px; color: #bdc3c7; margin-bottom: 20px;"></i>
          <h4 style="color: #7f8c8d;">Tiada Pelajar Dijumpai</h4>
          <p style="color: #95a5a6;">Tiada pelajar dalam kelas ini untuk penilaian tersebut.</p>
          <a href="mark_assessment.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Kembali
          </a>
        </div>
      <?php endif; ?>
    <?php endif; ?>
  </div>
</div>

<!-- Grading Scale Reference -->
<?php if ($selected_assessment && !$view_mode): ?>
<div style="margin-top: 30px; background-color: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #3498db;">
  <h5 style="margin-bottom: 15px; color: #2c3e50;">
    <i class="fas fa-info-circle"></i> Skala Gred
  </h5>
  <div class="row">
    <div class="col-md-6">
      <table class="table table-sm">
        <tr><td><span class="badge" style="background-color: #27ae60; color: white;">A+</span></td><td>90-100</td><td>Cemerlang Tertinggi</td></tr>
        <tr><td><span class="badge" style="background-color: #27ae60; color: white;">A</span></td><td>80-89</td><td>Cemerlang Tinggi</td></tr>
        <tr><td><span class="badge" style="background-color: #27ae60; color: white;">A-</span></td><td>70-79</td><td>Cemerlang</td></tr>
        <tr><td><span class="badge" style="background-color: #3498db; color: white;">B+</span></td><td>65-69</td><td>Kepujian Tertinggi</td></tr>
        <tr><td><span class="badge" style="background-color: #3498db; color: white;">B</span></td><td>60-64</td><td>Kepujian Tinggi</td></tr>
      </table>
    </div>
    <div class="col-md-6">
      <table class="table table-sm">
        <tr><td><span class="badge" style="background-color: #f39c12; color: white;">C+</span></td><td>55-59</td><td>Kepujian Atas</td></tr>
        <tr><td><span class="badge" style="background-color: #f39c12; color: white;">C</span></td><td>50-54</td><td>Kepujian</td></tr>
        <tr><td><span class="badge" style="background-color: #e67e22; color: white;">D</span></td><td>45-49</td><td>Lulus Atas</td></tr>
        <tr><td><span class="badge" style="background-color: #e67e22; color: white;">E</span></td><td>40-44</td><td>Lulus</td></tr>
        <tr><td><span class="badge" style="background-color: #e74c3c; color: white;">G</span></td><td>0-39</td><td>Gagal</td></tr>
      </table>
    </div>
  </div>
</div>
<?php endif; ?>

<script>
// Function to calculate grade based on marks
function calculateGradeJS(marks) {
    if (marks >= 90) return {grade: 'A+', description: 'Cemerlang Tertinggi', color: '#27ae60'};
    if (marks >= 80) return {grade: 'A', description: 'Cemerlang Tinggi', color: '#27ae60'};
    if (marks >= 70) return {grade: 'A-', description: 'Cemerlang', color: '#27ae60'};
    if (marks >= 65) return {grade: 'B+', description: 'Kepujian Tertinggi', color: '#3498db'};
    if (marks >= 60) return {grade: 'B', description: 'Kepujian Tinggi', color: '#3498db'};
    if (marks >= 55) return {grade: 'C+', description: 'Kepujian Atas', color: '#f39c12'};
    if (marks >= 50) return {grade: 'C', description: 'Kepujian', color: '#f39c12'};
    if (marks >= 45) return {grade: 'D', description: 'Lulus Atas', color: '#e67e22'};
    if (marks >= 40) return {grade: 'E', description: 'Lulus', color: '#e67e22'};
    return {grade: 'G', description: 'Gagal', color: '#e74c3c'};
}

// Function to update grade when marks change
function updateGrade(input, index) {
    // Convert to integer (whole number only)
    let marks = parseInt(input.value);

    // Update input value to ensure it's a whole number
    if (!isNaN(marks)) {
        input.value = marks;
    }

    const gradeCell = document.getElementById('grade-' + index);
    const descriptionCell = document.getElementById('description-' + index);

    if (isNaN(marks) || marks < 0 || marks > 100) {
        gradeCell.innerHTML = '<span class="badge bg-secondary">-</span>';
        descriptionCell.textContent = '-';
        return;
    }

    const gradeInfo = calculateGradeJS(marks);
    gradeCell.innerHTML = `<span class="badge" style="background-color: ${gradeInfo.color}; color: white; font-size: 14px; padding: 6px 10px;">${gradeInfo.grade}</span>`;
    descriptionCell.textContent = gradeInfo.description;
}

// Auto-save functionality and validation
document.addEventListener('DOMContentLoaded', function() {
    const marksInputs = document.querySelectorAll('.marks-input');

    marksInputs.forEach(input => {
        input.addEventListener('input', function() {
            // Validate input - whole numbers only
            let value = parseInt(this.value);
            if (isNaN(value)) {
                this.value = '';
                return;
            }
            if (value < 0) this.value = 0;
            if (value > 100) this.value = 100;

            // Add visual feedback
            if (this.value !== '') {
                this.style.backgroundColor = '#e8f5e8';
                this.style.borderColor = '#27ae60';
            } else {
                this.style.backgroundColor = '';
                this.style.borderColor = '#ddd';
            }
        });

        // Add focus effect
        input.addEventListener('focus', function() {
            this.style.borderColor = '#3498db';
        });

        input.addEventListener('blur', function() {
            if (this.value === '') {
                this.style.borderColor = '#ddd';
            }
        });
    });

    // Form submission validation
    const form = document.querySelector('form[method="post"]');
    if (form) {
        form.addEventListener('submit', function(e) {
            const inputs = form.querySelectorAll('.marks-input');
            let hasMarks = false;

            inputs.forEach(input => {
                if (input.value !== '') {
                    hasMarks = true;
                }
            });

            if (!hasMarks) {
                e.preventDefault();
                alert('Sila masukkan sekurang-kurangnya satu markah sebelum menyimpan.');
                return false;
            }
        });
    }
});
</script>

</body>
</html>
