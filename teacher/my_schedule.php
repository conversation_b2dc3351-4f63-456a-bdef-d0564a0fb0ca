<?php
session_start();

if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'Teacher') {
    header("Location: ../login.php");
    exit;
}

require '../db.php';

$teacher_user_id = $_SESSION['user_id'];

// Get teacher information
$teacherStmt = $conn->prepare("SELECT teacher_id, full_name FROM teachers WHERE user_id = ?");
$teacherStmt->bind_param("i", $teacher_user_id);
$teacherStmt->execute();
$teacherResult = $teacherStmt->get_result();
$teacher = $teacherResult->fetch_assoc();

if (!$teacher) {
    die("Error: Teacher not found");
}

$teacher_id = $teacher['teacher_id'];
$teacher_name = $teacher['full_name'];

// Get current academic year
$current_year = date('Y');
$academic_year = $current_year . '/' . ($current_year + 1);

// Get teacher's schedules
$schedules = [];
$schedulesQuery = $conn->prepare("
    SELECT s.*, sub.subject_name, c.class_name, c.tingkatan
    FROM schedules s
    JOIN subjects sub ON s.subject_id = sub.subject_id
    JOIN classrooms c ON s.classroom_id = c.classroom_id
    WHERE s.teacher_id = ? AND s.academic_year = ?
    ORDER BY 
        FIELD(s.day_name, 'Isnin', 'Selasa', 'Rabu', 'Khamis', 'Jumaat'),
        s.time_slot
");
$schedulesQuery->bind_param("is", $teacher_id, $academic_year);
$schedulesQuery->execute();
$schedulesResult = $schedulesQuery->get_result();
while ($schedule = $schedulesResult->fetch_assoc()) {
    $schedules[] = $schedule;
}

// Time slots for different sessions and days
$morning_slots_mon_thu = [
    '07:30-08:00' => 'Tempoh 1',
    '08:00-08:30' => 'Tempoh 2',
    '08:30-09:00' => 'Tempoh 3',
    '09:00-09:30' => 'Tempoh 4',
    '09:30-10:00' => 'Tempoh 5',
    '10:00-10:30' => 'REHAT PAGI',
    '10:30-11:00' => 'Tempoh 6',
    '11:00-11:30' => 'Tempoh 7',
    '11:30-12:00' => 'Tempoh 8',
    '12:00-12:30' => 'Tempoh 9',
    '12:30-13:00' => 'Tempoh 10',
    '13:00-13:30' => 'Tempoh 11',
    '13:30-14:00' => 'Tempoh 12',
    '14:00-14:30' => 'Tempoh 13'
];

// Special Monday morning slots (with Perhimpunan)
$morning_slots_monday = [
    '07:30-08:00' => 'PERHIMPUNAN',
    '08:00-08:30' => 'Tempoh 1',
    '08:30-09:00' => 'Tempoh 2',
    '09:00-09:30' => 'Tempoh 3',
    '09:30-10:00' => 'Tempoh 4',
    '10:00-10:30' => 'REHAT PAGI',
    '10:30-11:00' => 'Tempoh 5',
    '11:00-11:30' => 'Tempoh 6',
    '11:30-12:00' => 'Tempoh 7',
    '12:00-12:30' => 'Tempoh 8',
    '12:30-13:00' => 'Tempoh 9',
    '13:00-13:30' => 'Tempoh 10',
    '13:30-14:00' => 'Tempoh 11',
    '14:00-14:30' => 'Tempoh 12'
];

$morning_slots_friday = [
    '07:30-08:00' => 'YAASIN',
    '08:00-08:30' => 'Tempoh 1',
    '08:30-09:00' => 'Tempoh 2',
    '09:00-09:30' => 'Tempoh 3',
    '09:30-10:00' => 'Tempoh 4',
    '10:00-10:30' => 'REHAT PAGI',
    '10:30-11:00' => 'Tempoh 5',
    '11:00-11:30' => 'Tempoh 6',
    '11:30-12:00' => 'Tempoh 7'
];

$afternoon_slots_mon_thu = [
    '12:00-12:30' => 'Tempoh 1',
    '12:30-13:00' => 'Tempoh 2',
    '13:00-13:30' => 'Tempoh 3',
    '13:30-14:00' => 'Tempoh 4',
    '14:00-14:30' => 'Tempoh 5',
    '14:30-15:00' => 'Tempoh 6',
    '15:00-15:30' => 'Tempoh 7',
    '15:30-16:00' => 'Tempoh 8',
    '16:00-16:30' => 'Tempoh 9',
    '16:30-17:00' => 'Tempoh 10',
    '17:00-17:30' => 'Tempoh 11',
    '17:30-18:00' => 'Tempoh 12',
    '18:00-18:30' => 'Tempoh 13'
];

$afternoon_slots_friday = [
    '14:45-15:15' => 'Tempoh 1',
    '15:15-15:45' => 'Tempoh 2',
    '15:45-16:15' => 'Tempoh 3',
    '16:15-16:45' => 'Tempoh 4',
    '16:45-17:15' => 'Tempoh 5',
    '17:15-17:45' => 'Tempoh 6',
    '17:45-18:15' => 'Tempoh 7',
    '18:15-18:40' => 'Tempoh 8'
];

// Function to get time slots for a specific day and session
function getTimeSlots($day, $session) {
    global $morning_slots_mon_thu, $morning_slots_monday, $morning_slots_friday, $afternoon_slots_mon_thu, $afternoon_slots_friday;

    if ($session === 'morning') {
        if ($day === 'Isnin') {
            return $morning_slots_monday;
        } elseif ($day === 'Jumaat') {
            return $morning_slots_friday;
        } else {
            return $morning_slots_mon_thu;
        }
    } else { // afternoon
        if ($day === 'Jumaat') {
            return $afternoon_slots_friday;
        } else {
            return $afternoon_slots_mon_thu;
        }
    }
}

$days = ['Isnin', 'Selasa', 'Rabu', 'Khamis', 'Jumaat'];
$sessions = ['morning' => 'Sesi Pagi', 'afternoon' => 'Sesi Petang'];

// Count total classes per week (excluding breaks)
$total_classes = count(array_filter($schedules, function($s) {
    return strpos($s['time_slot'], '10:00-10:30') === false; // Exclude break time
}));

// Get subjects taught
$subjects_taught = array_unique(array_column($schedules, 'subject_name'));
$classes_taught = array_unique(array_map(function($s) {
    return $s['class_name'] . ' (T' . $s['tingkatan'] . ')';
}, $schedules));
?>

<!DOCTYPE html>
<html lang="ms">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Jadual Waktu Saya - SMKTMI</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>

<style>
.content {
    margin-left: 300px;
    padding: 20px;
    min-height: 100vh;
    background-color: #f8f9fa;
}

.schedule-container {
    max-width: 1400px;
    margin: 0 auto;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
    overflow: hidden;
}

.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
    text-align: center;
}

.header h1 {
    font-size: 32px;
    font-weight: 300;
    margin: 0 0 10px 0;
    letter-spacing: 1px;
}

.header p {
    font-size: 16px;
    margin: 0;
    opacity: 0.9;
}

.session-section {
    padding: 30px;
    border-bottom: 1px solid #eee;
}

.session-section:last-child {
    border-bottom: none;
}

.session-title {
    font-size: 20px;
    font-weight: 600;
    color: #495057;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #e9ecef;
}

.schedule-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.schedule-table th {
    background-color: #f8f9fa;
    color: #495057;
    font-weight: 600;
    padding: 15px 8px;
    text-align: center;
    border: 1px solid #dee2e6;
    font-size: 13px;
}

.schedule-table td {
    padding: 8px;
    text-align: center;
    vertical-align: middle;
    border: 1px solid #dee2e6;
    min-height: 60px;
}

.time-cell {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #495057;
    width: 100px;
    font-size: 12px;
}

.schedule-card {
    background: #667eea;
    color: white;
    padding: 8px 6px;
    border-radius: 6px;
    margin: 1px;
    font-size: 12px;
    line-height: 1.3;
    min-height: 45px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.schedule-card .subject {
    font-weight: 600;
    margin-bottom: 2px;
}

.schedule-card .class {
    font-size: 11px;
    opacity: 0.9;
}

.rehat-cell {
    background-color: #f8f9fa !important;
    color: #6c757d;
    font-style: italic;
    font-weight: 500;
}

.current-day {
    background-color: #fff3cd !important;
}

.today-highlight {
    background: #28a745 !important;
}

.empty-slot {
    color: #adb5bd;
    font-size: 12px;
}

.print-btn {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #667eea;
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 25px;
    font-size: 14px;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    transition: all 0.3s ease;
}

.print-btn:hover {
    background: #5a6fd8;
    transform: translateY(-2px);
}

@media print {
    .content { margin-left: 0 !important; padding: 10px !important; }
    .print-btn { display: none !important; }
    .schedule-container { box-shadow: none !important; }
}
</style>

<?php include 'includes/sidebar.php'; ?>

<div class="content">
  <button class="print-btn" onclick="window.print()">
    <i class="fas fa-print"></i> Cetak
  </button>

  <div class="schedule-container">
    <!-- Header -->
    <div class="header">
      <h1>Jadual Waktu</h1>
      <p><?php echo htmlspecialchars($teacher_name); ?> • <?php echo $academic_year; ?></p>
    </div>

    <!-- Print-only title -->
    <div class="print-only-header" style="display: none;">
      <div style="text-align: center; padding: 20px; border-bottom: 2px solid #000;">
        <h2 style="margin: 0; font-size: 18px; font-weight: bold;">JADUAL WAKTU GURU</h2>
        <h3 style="margin: 5px 0; font-size: 16px;">SMK TUNKU MAHMOOD ISKANDAR (SMKTMI)</h3>
        <p style="margin: 5px 0; font-size: 14px;">
          <strong>Guru:</strong> <?php echo htmlspecialchars($teacher_name); ?> &nbsp;&nbsp;&nbsp;
          <strong>Tahun Akademik:</strong> <?php echo $academic_year; ?>
        </p>
      </div>
    </div>

    <?php
    $today = date('l');
    $today_malay = [
        'Monday' => 'Isnin',
        'Tuesday' => 'Selasa',
        'Wednesday' => 'Rabu',
        'Thursday' => 'Khamis',
        'Friday' => 'Jumaat'
    ][$today] ?? '';

    foreach ($sessions as $session_key => $session_name):
    ?>
    <div class="session-section">
      <div class="session-title"><?php echo $session_name; ?></div>

      <table class="schedule-table">
        <thead>
          <tr>
            <th class="time-cell">Masa</th>
            <?php foreach ($days as $day): ?>
            <th class="<?php echo ($day === $today_malay) ? 'current-day' : ''; ?>">
              <?php echo $day; ?>
            </th>
            <?php endforeach; ?>
          </tr>
        </thead>
        <tbody>
          <?php
          // Get all possible time slots for this session
          $all_time_slots = [];
          foreach ($days as $day) {
              $day_slots = getTimeSlots($day, $session_key);
              foreach ($day_slots as $time_slot => $period_name) {
                  if (!isset($all_time_slots[$time_slot])) {
                      $all_time_slots[$time_slot] = $period_name;
                  }
              }
          }

          foreach ($all_time_slots as $time_slot => $period_name):
          ?>
          <tr>
            <td class="time-cell <?php echo (strpos($period_name, 'REHAT') !== false) ? 'rehat-cell' : ''; ?>">
              <?php echo $time_slot; ?>
            </td>
            <?php foreach ($days as $day): ?>
            <?php
            $day_slots = getTimeSlots($day, $session_key);
            $day_has_slot = isset($day_slots[$time_slot]);
            $day_period_name = $day_slots[$time_slot] ?? $period_name;
            ?>
            <td class="<?php echo (strpos($day_period_name, 'REHAT') !== false || strpos($day_period_name, 'PERHIMPUNAN') !== false || strpos($day_period_name, 'YAASIN') !== false) ? 'rehat-cell' : ''; ?> <?php echo ($day === $today_malay) ? 'current-day' : ''; ?>">
              <?php if (!$day_has_slot): ?>
                <span class="empty-slot">-</span>
              <?php elseif (strpos($day_period_name, 'REHAT') !== false): ?>
                Rehat
              <?php elseif (strpos($day_period_name, 'PERHIMPUNAN') !== false): ?>
                <strong>Perhimpunan</strong>
              <?php elseif (strpos($day_period_name, 'YAASIN') !== false): ?>
                <strong>Yaasin</strong>
              <?php else: ?>
                <?php
                $daySchedules = array_filter($schedules, function($s) use ($day, $time_slot) {
                    return $s['day_name'] === $day && $s['time_slot'] === $time_slot;
                });

                foreach ($daySchedules as $schedule):
                ?>
                <div class="schedule-card <?php echo ($day === $today_malay) ? 'today-highlight' : ''; ?>">
                  <div class="subject"><?php echo htmlspecialchars($schedule['subject_name']); ?></div>
                  <div class="class"><?php echo htmlspecialchars($schedule['tingkatan'] . ' ' . $schedule['class_name']); ?></div>
                </div>
                <?php endforeach; ?>

                <?php if (empty($daySchedules)): ?>
                <span class="empty-slot">-</span>
                <?php endif; ?>
              <?php endif; ?>
            </td>
            <?php endforeach; ?>
          </tr>
          <?php endforeach; ?>
        </tbody>
      </table>
    </div>
    <?php endforeach; ?>

    <?php if (empty($schedules)): ?>
    <div class="session-section">
      <div style="text-align: center; padding: 40px; color: #6c757d;">
        <i class="fas fa-calendar-times" style="font-size: 48px; margin-bottom: 20px;"></i>
        <h4>Tiada Jadual</h4>
        <p>Anda belum mempunyai jadual waktu yang ditetapkan.<br>Sila hubungi pentadbir sistem.</p>
      </div>
    </div>
    <?php endif; ?>

  </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

<style media="print">
/* Hide sidebar completely */
.sidebar {
    display: none !important;
}

/* Reset content margin and padding */
.content {
    margin-left: 0 !important;
    margin: 0 !important;
    padding: 15px !important;
    width: 100% !important;
}

/* Hide print button */
.print-btn {
    display: none !important;
}

/* Clean container styling */
.schedule-container {
    box-shadow: none !important;
    border: none !important;
    margin: 0 !important;
    max-width: none !important;
}

/* Hide regular header in print */
.header {
    display: none !important;
}

/* Show print-only header */
.print-only-header {
    display: block !important;
    page-break-inside: avoid;
}

/* Table styling for print */
.schedule-table {
    width: 100% !important;
    font-size: 11px !important;
}

.schedule-table th,
.schedule-table td {
    border: 1px solid #000 !important;
    padding: 6px 4px !important;
    font-size: 10px !important;
}

.schedule-table th {
    background: #f0f0f0 !important;
    font-weight: bold !important;
}

/* Schedule card styling for print */
.schedule-card {
    background: #f8f9fa !important;
    color: #000 !important;
    border: 1px solid #666 !important;
    margin: 1px !important;
    padding: 4px !important;
    font-size: 9px !important;
    line-height: 1.2 !important;
}

/* Session styling */
.session-section {
    page-break-inside: avoid;
    padding: 10px 0 !important;
    border-bottom: 1px solid #ccc !important;
}

.session-title {
    font-size: 14px !important;
    font-weight: bold !important;
    margin-bottom: 10px !important;
    border-bottom: 2px solid #000 !important;
    padding-bottom: 5px !important;
}

/* Time cell styling */
.time-cell {
    background: #f0f0f0 !important;
    font-weight: bold !important;
    width: 80px !important;
}

/* Rehat cell styling */
.rehat-cell {
    background: #f8f9fa !important;
    font-style: italic !important;
}

/* Remove any background colors that might interfere */
* {
    -webkit-print-color-adjust: exact !important;
    color-adjust: exact !important;
}
</style>

</body>
</html>
