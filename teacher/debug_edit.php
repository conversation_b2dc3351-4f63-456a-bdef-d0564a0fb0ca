<?php
session_start();

if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'Teacher') {
    header("Location: ../login.php");
    exit;
}

require '../db.php';

// Get teacher ID
$teacher_user_id = $_SESSION['user_id'];
$stmt = $conn->prepare("SELECT teacher_id, full_name FROM teachers WHERE user_id = ?");
$stmt->bind_param("i", $teacher_user_id);
$stmt->execute();
$teacher = $stmt->get_result()->fetch_assoc();
$teacher_id = $teacher['teacher_id'];

echo "<!DOCTYPE html><html><head><title>Debug Edit</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "</head><body><div class='container mt-5'>";

echo "<h2>Debug Edit Functionality</h2>";

// Get first incident for testing
$incident = null;
$incidentQuery = $conn->prepare("
    SELECT di.*, s.full_name as student_name, c.class_name 
    FROM discipline_incidents di 
    JOIN students s ON di.student_id = s.student_id 
    LEFT JOIN classrooms c ON s.classroom_id = c.classroom_id 
    WHERE di.teacher_id = ? 
    ORDER BY di.created_at DESC 
    LIMIT 1
");
$incidentQuery->bind_param("i", $teacher_id);
$incidentQuery->execute();
$result = $incidentQuery->get_result();

if ($result->num_rows > 0) {
    $incident = $result->fetch_assoc();
    
    echo "<div class='alert alert-info'>";
    echo "<h5>Test Incident Found:</h5>";
    echo "<p><strong>ID:</strong> {$incident['incident_id']}</p>";
    echo "<p><strong>Student:</strong> {$incident['student_name']}</p>";
    echo "<p><strong>Date:</strong> {$incident['incident_date']}</p>";
    echo "<p><strong>Type:</strong> {$incident['incident_type']}</p>";
    echo "<p><strong>Status:</strong> {$incident['status']}</p>";
    echo "</div>";
    
    // Test form
    echo "<div class='card'>";
    echo "<div class='card-header'><h5>Test Edit Form</h5></div>";
    echo "<div class='card-body'>";
    
    echo "<form id='testForm'>";
    echo "<div class='row'>";
    echo "<div class='col-md-6'>";
    echo "<label>Incident ID:</label>";
    echo "<input type='text' id='incident_id' class='form-control' value='{$incident['incident_id']}' readonly>";
    echo "</div>";
    echo "<div class='col-md-6'>";
    echo "<label>Date:</label>";
    echo "<input type='date' id='incident_date' class='form-control' value='{$incident['incident_date']}'>";
    echo "</div>";
    echo "</div>";
    
    echo "<div class='row mt-3'>";
    echo "<div class='col-md-6'>";
    echo "<label>Type:</label>";
    echo "<select id='incident_type' class='form-select'>";
    echo "<option value='Ponteng Kelas'" . ($incident['incident_type'] == 'Ponteng Kelas' ? ' selected' : '') . ">Ponteng Kelas</option>";
    echo "<option value='Bergaduh'" . ($incident['incident_type'] == 'Bergaduh' ? ' selected' : '') . ">Bergaduh</option>";
    echo "<option value='Buli'" . ($incident['incident_type'] == 'Buli' ? ' selected' : '') . ">Buli</option>";
    echo "</select>";
    echo "</div>";
    echo "<div class='col-md-6'>";
    echo "<label>Status:</label>";
    echo "<select id='status' class='form-select'>";
    echo "<option value='Baru'" . ($incident['status'] == 'Baru' ? ' selected' : '') . ">Baru</option>";
    echo "<option value='Dalam Tindakan'" . ($incident['status'] == 'Dalam Tindakan' ? ' selected' : '') . ">Dalam Tindakan</option>";
    echo "<option value='Selesai'" . ($incident['status'] == 'Selesai' ? ' selected' : '') . ">Selesai</option>";
    echo "</select>";
    echo "</div>";
    echo "</div>";
    
    echo "<div class='row mt-3'>";
    echo "<div class='col-md-6'>";
    echo "<label>Severity:</label>";
    echo "<select id='severity' class='form-select'>";
    echo "<option value='Ringan'" . ($incident['severity'] == 'Ringan' ? ' selected' : '') . ">Ringan</option>";
    echo "<option value='Sederhana'" . ($incident['severity'] == 'Sederhana' ? ' selected' : '') . ">Sederhana</option>";
    echo "<option value='Berat'" . ($incident['severity'] == 'Berat' ? ' selected' : '') . ">Berat</option>";
    echo "</select>";
    echo "</div>";
    echo "</div>";
    
    echo "<div class='mt-3'>";
    echo "<label>Description:</label>";
    echo "<textarea id='description' class='form-control' rows='3'>{$incident['description']}</textarea>";
    echo "</div>";
    
    echo "<div class='mt-3'>";
    echo "<label>Action Taken:</label>";
    echo "<textarea id='action_taken' class='form-control' rows='2'>{$incident['action_taken']}</textarea>";
    echo "</div>";
    
    echo "<div class='mt-3'>";
    echo "<button type='button' onclick='testUpdate()' class='btn btn-primary'>Test Update</button>";
    echo "<button type='button' onclick='showFormData()' class='btn btn-info ms-2'>Show Form Data</button>";
    echo "</div>";
    
    echo "</form>";
    echo "</div>";
    echo "</div>";
    
    echo "<div class='mt-4'>";
    echo "<div id='result'></div>";
    echo "</div>";
    
} else {
    echo "<div class='alert alert-warning'>";
    echo "<h5>No Incidents Found</h5>";
    echo "<p>You need to create at least one discipline incident to test the edit functionality.</p>";
    echo "<a href='discipline_report.php' class='btn btn-primary'>Create New Incident</a>";
    echo "</div>";
}

echo "<div class='mt-4'>";
echo "<a href='discipline_report.php' class='btn btn-secondary'>Back to Main Form</a>";
echo "</div>";

echo "</div>";

echo "<script>";
echo "function showFormData() {";
echo "  const formData = {";
echo "    incident_id: document.getElementById('incident_id').value,";
echo "    incident_date: document.getElementById('incident_date').value,";
echo "    incident_type: document.getElementById('incident_type').value,";
echo "    status: document.getElementById('status').value,";
echo "    severity: document.getElementById('severity').value,";
echo "    description: document.getElementById('description').value,";
echo "    action_taken: document.getElementById('action_taken').value";
echo "  };";
echo "  document.getElementById('result').innerHTML = '<div class=\"alert alert-info\"><h6>Form Data:</h6><pre>' + JSON.stringify(formData, null, 2) + '</pre></div>';";
echo "}";

echo "function testUpdate() {";
echo "  const formData = new FormData();";
echo "  formData.append('incident_id', document.getElementById('incident_id').value);";
echo "  formData.append('incident_date', document.getElementById('incident_date').value);";
echo "  formData.append('incident_type', document.getElementById('incident_type').value);";
echo "  formData.append('status', document.getElementById('status').value);";
echo "  formData.append('severity', document.getElementById('severity').value);";
echo "  formData.append('description', document.getElementById('description').value);";
echo "  formData.append('action_taken', document.getElementById('action_taken').value);";

echo "  document.getElementById('result').innerHTML = '<div class=\"alert alert-info\">Sending update request...</div>';";

echo "  fetch('update_incident.php', {";
echo "    method: 'POST',";
echo "    body: formData";
echo "  })";
echo "  .then(response => response.json())";
echo "  .then(data => {";
echo "    if (data.success) {";
echo "      document.getElementById('result').innerHTML = '<div class=\"alert alert-success\"><h6>✅ Success!</h6><p>' + data.message + '</p></div>';";
echo "    } else {";
echo "      document.getElementById('result').innerHTML = '<div class=\"alert alert-danger\"><h6>❌ Error!</h6><p>' + data.message + '</p></div>';";
echo "    }";
echo "  })";
echo "  .catch(error => {";
echo "    document.getElementById('result').innerHTML = '<div class=\"alert alert-danger\"><h6>❌ Network Error!</h6><p>' + error.message + '</p></div>';";
echo "  });";
echo "}";
echo "</script>";

echo "</body></html>";
?>
