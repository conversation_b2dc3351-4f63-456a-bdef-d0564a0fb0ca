<?php
session_start();
require '../db.php';
include 'includes/header.php';
include 'includes/sidebar.php';

$teacher_user_id = $_SESSION['user_id'];
$today = date('Y-m-d');

// Dapatkan teacher_id
$stmt = $conn->prepare("SELECT teacher_id FROM teachers WHERE user_id = ?");
$stmt->bind_param("i", $teacher_user_id);
$stmt->execute();
$teacher = $stmt->get_result()->fetch_assoc();
$teacher_id = $teacher['teacher_id'] ?? 0;

// Dapatkan classroom guru
$stmt2 = $conn->prepare("SELECT classroom_id, class_name FROM classrooms WHERE teacher_id = ?");
$stmt2->bind_param("i", $teacher_id);
$stmt2->execute();
$classroom = $stmt2->get_result()->fetch_assoc();

if (!$classroom) {
    echo "<div class='alert alert-danger m-4'>❌ Anda bukan guru kelas. <PERSON>ks<PERSON> ditolak.</div>";
    exit;
}

$classroom_id = $classroom['classroom_id'];
$class_name = $classroom['class_name'];

// Pilihan bulan dan tahun
$selected_month = isset($_GET['month']) ? (int)$_GET['month'] : date('m');
$selected_year = isset($_GET['year']) ? (int)$_GET['year'] : date('Y');

// Dapatkan bilangan hari dalam bulan
$days_in_month = cal_days_in_month(CAL_GREGORIAN, $selected_month, $selected_year);

// Senarai pelajar ikut student_id
$stmt3 = $conn->prepare("SELECT student_id, full_name FROM students WHERE classroom_id = ? ORDER BY student_id ASC");
$stmt3->bind_param("i", $classroom_id);
$stmt3->execute();
$students = $stmt3->get_result()->fetch_all(MYSQLI_ASSOC);

// Dapatkan semua kehadiran bulan itu
$start_date = "$selected_year-$selected_month-01";
$end_date = "$selected_year-$selected_month-$days_in_month";

$stmt4 = $conn->prepare("SELECT student_id, attendance_date, status FROM attendance WHERE classroom_id = ? AND attendance_date BETWEEN ? AND ?");
$stmt4->bind_param("iss", $classroom_id, $start_date, $end_date);
$stmt4->execute();
$attendanceData = $stmt4->get_result()->fetch_all(MYSQLI_ASSOC);

// Susun ke dalam format [student_id][day] = status
$attendanceMap = [];
foreach ($attendanceData as $row) {
    $day = (int)date('j', strtotime($row['attendance_date'])); // 1-31
    $attendanceMap[$row['student_id']][$day] = $row['status'];
}
?>

<!DOCTYPE html>
<html lang="ms">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Maklumat Kehadiran Pelajar - SMKTMI</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>

<style>
/* Content Styling */
.content {
    margin-left: 280px;
    padding: 40px 30px;
    min-height: 100vh;
    background-color: #f9f9f9;
}

.form-container {
    width: 100%;
    max-width: 1400px;
    background-color: #fff;
    padding: 30px 40px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0 auto;
}

.form-container h2 {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 25px;
    border-bottom: 3px solid #3498db;
    padding-bottom: 10px;
    color: #2980b9;
    text-align: center;
}

.filter-section {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 30px;
    border-left: 4px solid #3498db;
}

.btn-primary {
    background-color: #3498db;
    border-color: #3498db;
    padding: 10px 30px;
    font-weight: 600;
}

.btn-primary:hover {
    background-color: #2980b9;
    border-color: #2980b9;
}

.attendance-table {
    font-size: 12px;
}

.attendance-table th {
    background-color: #3498db;
    color: white;
    text-align: center;
    vertical-align: middle;
    padding: 8px 4px;
}

.attendance-table td {
    text-align: center;
    vertical-align: middle;
    padding: 6px 4px;
}

/* Responsive */
@media (max-width: 768px) {
    .content {
        margin-left: 0;
        padding: 20px;
    }
    .form-container {
        padding: 20px;
    }
}
</style>

<?php include 'includes/sidebar.php'; ?>

<div class="content">
  <div class="form-container">
    <div class="container-fluid mt-4">
        <h3>Kehadiran Bulanan - <?php echo htmlspecialchars($class_name); ?></h3>





        <form method="get" class="row mb-4">
            <div class="col-md-2">
                <select name="month" class="form-select">
                    <?php for ($m = 1; $m <= 12; $m++): ?>
                        <option value="<?php echo $m; ?>" <?php if ($m == $selected_month) echo 'selected'; ?>>

                            <?php
$bulan_melayu = [
    1 => 'Januari',
    2 => 'Februari',
    3 => 'Mac',
    4 => 'April',
    5 => 'Mei',
    6 => 'Jun',
    7 => 'Julai',
    8 => 'Ogos',
    9 => 'September',
    10 => 'Oktober',
    11 => 'November',
    12 => 'Disember'
];
echo $bulan_melayu[$m];
?>
                        </option>
                    <?php endfor; ?>
                </select>
            </div>
            <div class="col-md-2">
                <select name="year" class="form-select">
                    <?php for ($y = date('Y'); $y >= 2020; $y--): ?>
                        <option value="<?php echo $y; ?>" <?php if ($y == $selected_year) echo 'selected'; ?>>
                            <?php echo $y; ?>
                        </option>
                    <?php endfor; ?>
                </select>
            </div>
            <div class="col-md-2">
                <button type="submit" class="btn btn-primary">Tapis</button>
            </div>
        </form>

        <div class="table-responsive">
            <table class="table table-bordered small">
                <thead>
                    <tr class="text-center">
                        <th>Nama Pelajar</th>
                        <?php for ($d = 1; $d <= $days_in_month; $d++): ?>
                            <th><?php echo $d; ?></th>
                        <?php endfor; ?>
                        <th>Hadir</th>
                        <th>Tidak Hadir</th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    // Initialize daily totals array
                    $dailyTotals = [];
                    for ($d = 1; $d <= $days_in_month; $d++) {
                        $dailyTotals[$d] = ['hadir' => 0, 'tidak_hadir' => 0, 'total' => 0];
                    }

                    foreach ($students as $student):
                        $hadirCount = 0;
                        $tidakHadirCount = 0;
                    ?>
                        <tr>
                            <td><?php echo htmlspecialchars($student['full_name']); ?></td>
                            <?php for ($d = 1; $d <= $days_in_month; $d++):
                                $status = $attendanceMap[$student['student_id']][$d] ?? '';
                                if ($status === 'Hadir') {
                                    $hadirCount++;
                                    $dailyTotals[$d]['hadir']++;
                                    $dailyTotals[$d]['total']++;
                                } else if ($status === 'Tidak Hadir') {
                                    $tidakHadirCount++;
                                    $dailyTotals[$d]['tidak_hadir']++;
                                    $dailyTotals[$d]['total']++;
                                }

                                $bgClass = '';
                                if ($status === 'Hadir') $bgClass = 'bg-success text-white';
                                else if ($status === 'Tidak Hadir') $bgClass = 'bg-danger text-white';

                                $displayText = $status === 'Hadir' ? 'H' : ($status === 'Tidak Hadir' ? 'T' : '-');
                            ?>
                                <td class="text-center <?php echo $bgClass; ?>"><?php echo $displayText; ?></td>
                            <?php endfor; ?>
                            <td class="text-center fw-bold"><?php echo $hadirCount; ?></td>
                            <td class="text-center fw-bold"><?php echo $tidakHadirCount; ?></td>
                        </tr>
                    <?php endforeach; ?>

                    <!-- Daily Totals Row -->
                    <tr style="background-color: #e9ecef; font-weight: bold; border-top: 3px solid #3498db;">
                        <td class="text-center" style="background-color: #3498db; color: white;">JUMLAH HADIR</td>
                        <?php for ($d = 1; $d <= $days_in_month; $d++): ?>
                            <td class="text-center" style="color: #2c3e50; font-size: 14px;">
                                <?php echo $dailyTotals[$d]['hadir']; ?>
                            </td>
                        <?php endfor; ?>
                        <td class="text-center" style="color: #27ae60;">-</td>
                        <td class="text-center" style="color: #e74c3c;">-</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
