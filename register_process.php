<?php
require 'db.php';
require 'send_email.php';

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Function to send JSON response and exit
function sendResponse($success, $message) {
    header('Content-Type: application/json');
    echo json_encode(['success' => $success, 'message' => $message]);
    exit;
}

// Validate input data
if (!isset($_POST['full_name'], $_POST['email'], $_POST['phone_number'], $_POST['address'], $_POST['password'], $_POST['confirm_password'])) {
    sendResponse(false, "Semua medan diperlukan.");
}

$full_name = trim($_POST['full_name']);
$email = trim($_POST['email']);
$phone_number = trim($_POST['phone_number']);
$address = trim($_POST['address']);
$password = $_POST['password'];
$confirm_password = $_POST['confirm_password'];

// Validate email format
if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
    sendResponse(false, "Format emel tidak sah.");
}

// Validate required fields
if (empty($full_name) || empty($email) || empty($phone_number) || empty($address)) {
    sendResponse(false, "Semua medan diperlukan.");
}

// Validate password match
if ($password !== $confirm_password) {
    sendResponse(false, "Kata laluan tidak sepadan.");
}

// Validate password policy
if (!preg_match('/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_]).{8,}$/', $password)) {
    sendResponse(false, "Kata laluan tidak ikut polisi. Kata laluan mesti mengandungi sekurang-kurangnya 8 aksara, huruf besar, huruf kecil, nombor dan simbol.");
}

// Check if email already exists
$checkStmt = $conn->prepare("SELECT user_id FROM users WHERE email = ?");
if (!$checkStmt) {
    sendResponse(false, "Ralat sistem: " . $conn->error);
}
$checkStmt->bind_param("s", $email);
$checkStmt->execute();
$result = $checkStmt->get_result();

if ($result->num_rows > 0) {
    $checkStmt->close();
    sendResponse(false, "Emel ini telah digunakan. Sila guna emel lain.");
}
$checkStmt->close();

// Start transaction
$conn->autocommit(false);

try {
    $password_hash = password_hash($password, PASSWORD_DEFAULT);
    $role_id = 2;
    $verify_token = bin2hex(random_bytes(16));
    $token_created_at = date('Y-m-d H:i:s');

    // Insert into users
    $stmt = $conn->prepare("INSERT INTO users (email, password_hash, role_id) VALUES (?, ?, ?)");
    if (!$stmt) {
        throw new Exception("Prepare failed for users: " . $conn->error);
    }

    $stmt->bind_param("ssi", $email, $password_hash, $role_id);
    if (!$stmt->execute()) {
        throw new Exception("Failed to insert user: " . $stmt->error);
    }

    $user_id = $stmt->insert_id;
    $stmt->close();

    // Insert into parents (including email field)
    $stmt = $conn->prepare("INSERT INTO parents (user_id, full_name, phone_number, address, email, verification_token, token_created_at, email_verified) VALUES (?, ?, ?, ?, ?, ?, ?, 0)");
    if (!$stmt) {
        throw new Exception("Prepare failed for parents: " . $conn->error);
    }

    $stmt->bind_param("issssss", $user_id, $full_name, $phone_number, $address, $email, $verify_token, $token_created_at);
    if (!$stmt->execute()) {
        throw new Exception("Failed to insert parent: " . $stmt->error);
    }
    $stmt->close();

    // Try to send verification email
    $verify_link = "http://***************/smktmi/verify.php?token=$verify_token";
    $emailResult = sendVerificationEmail($email, $verify_link);

    if (!$emailResult['success']) {
        throw new Exception("Gagal menghantar emel pengesahan: " . $emailResult['message']);
    }

    // Commit transaction
    $conn->commit();
    $conn->autocommit(true);

    sendResponse(true, "Pendaftaran berjaya! Sila semak emel anda untuk pengesahan.");

} catch (Exception $e) {
    // Rollback transaction
    $conn->rollback();
    $conn->autocommit(true);

    // Log the error for debugging
    error_log("Registration error: " . $e->getMessage());

    sendResponse(false, "Ralat semasa pendaftaran: " . $e->getMessage());
}
?>
