<?php
require 'db.php';
require 'send_email.php';

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Function to send response (JSON for AJAX, HTML for regular form)
function sendResponse($success, $message) {
    // Check if this is an AJAX request
    $isAjax = !empty($_SERVER['HTTP_X_REQUESTED_WITH']) &&
              strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest';

    // Also check for Accept header containing application/json
    $acceptsJson = isset($_SERVER['HTTP_ACCEPT']) &&
                   strpos($_SERVER['HTTP_ACCEPT'], 'application/json') !== false;

    if ($isAjax || $acceptsJson) {
        // Send JSON response for AJAX requests
        header('Content-Type: application/json');
        echo json_encode(['success' => $success, 'message' => $message]);
    } else {
        // Send HTML response for regular form submissions
        ?>
        <!DOCTYPE html>
        <html lang="ms">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title><?php echo $success ? 'Pendaftaran Berjaya' : 'Ralat Pendaftaran'; ?> - SMKTMI</title>
            <style>
                body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f0f2f5; margin: 0; padding: 40px; }
                .container { max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 8px 24px rgba(0,0,0,0.1); }
                .success { color: #388e3c; }
                .error { color: #d32f2f; }
                .btn { display: inline-block; padding: 12px 24px; background-color: #007bff; color: white; text-decoration: none; border-radius: 5px; margin-top: 20px; }
                .btn:hover { background-color: #0056b3; }
                .btn.success { background-color: #28a745; }
                .btn.success:hover { background-color: #1e7e34; }
            </style>
        </head>
        <body>
            <div class="container">
                <h2 class="<?php echo $success ? 'success' : 'error'; ?>">
                    <?php echo $success ? '✓ Pendaftaran Berjaya' : '❌ Ralat Pendaftaran'; ?>
                </h2>
                <p><?php echo htmlspecialchars($message); ?></p>
                <?php if ($success): ?>
                    <a href="login.php" class="btn success">Log Masuk Sekarang</a>
                <?php else: ?>
                    <a href="register.php" class="btn">Cuba Lagi</a>
                <?php endif; ?>
            </div>
        </body>
        </html>
        <?php
    }
    exit;
}

// Validate input data
if (!isset($_POST['full_name'], $_POST['email'], $_POST['phone_number'], $_POST['address'], $_POST['password'], $_POST['confirm_password'])) {
    sendResponse(false, "Semua medan diperlukan.");
}

$full_name = trim($_POST['full_name']);
$email = trim($_POST['email']);
$phone_number = trim($_POST['phone_number']);
$address = trim($_POST['address']);
$password = $_POST['password'];
$confirm_password = $_POST['confirm_password'];

// Validate email format
if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
    sendResponse(false, "Format emel tidak sah.");
}

// Validate required fields
if (empty($full_name) || empty($email) || empty($phone_number) || empty($address)) {
    sendResponse(false, "Semua medan diperlukan.");
}

// Validate password match
if ($password !== $confirm_password) {
    sendResponse(false, "Kata laluan tidak sepadan.");
}

// Validate password policy
if (!preg_match('/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_]).{8,}$/', $password)) {
    sendResponse(false, "Kata laluan tidak ikut polisi. Kata laluan mesti mengandungi sekurang-kurangnya 8 aksara, huruf besar, huruf kecil, nombor dan simbol.");
}

// Check if email already exists
$checkStmt = $conn->prepare("SELECT user_id FROM users WHERE email = ?");
if (!$checkStmt) {
    sendResponse(false, "Ralat sistem: " . $conn->error);
}
$checkStmt->bind_param("s", $email);
$checkStmt->execute();
$result = $checkStmt->get_result();

if ($result->num_rows > 0) {
    $checkStmt->close();
    sendResponse(false, "Emel ini telah digunakan. Sila guna emel lain.");
}
$checkStmt->close();

// Start transaction
$conn->autocommit(false);

try {
    $password_hash = password_hash($password, PASSWORD_DEFAULT);
    $role_id = 2;
    $verify_token = bin2hex(random_bytes(16));
    $token_created_at = date('Y-m-d H:i:s');

    // Insert into users
    $stmt = $conn->prepare("INSERT INTO users (email, password_hash, role_id) VALUES (?, ?, ?)");
    if (!$stmt) {
        throw new Exception("Prepare failed for users: " . $conn->error);
    }

    $stmt->bind_param("ssi", $email, $password_hash, $role_id);
    if (!$stmt->execute()) {
        throw new Exception("Failed to insert user: " . $stmt->error);
    }

    $user_id = $stmt->insert_id;
    $stmt->close();

    // Insert into parents (including email field)
    $stmt = $conn->prepare("INSERT INTO parents (user_id, full_name, phone_number, address, email, verification_token, token_created_at, email_verified) VALUES (?, ?, ?, ?, ?, ?, ?, 0)");
    if (!$stmt) {
        throw new Exception("Prepare failed for parents: " . $conn->error);
    }

    $stmt->bind_param("issssss", $user_id, $full_name, $phone_number, $address, $email, $verify_token, $token_created_at);
    if (!$stmt->execute()) {
        throw new Exception("Failed to insert parent: " . $stmt->error);
    }
    $stmt->close();

    // Try to send verification email
    $verify_link = "http://***************/smktmi/verify.php?token=$verify_token";
    $emailResult = sendVerificationEmail($email, $verify_link);

    if (!$emailResult['success']) {
        throw new Exception("Gagal menghantar emel pengesahan: " . $emailResult['message']);
    }

    // Commit transaction
    $conn->commit();
    $conn->autocommit(true);

    sendResponse(true, "Pendaftaran berjaya! Sila semak emel anda untuk pengesahan.");

} catch (Exception $e) {
    // Rollback transaction
    $conn->rollback();
    $conn->autocommit(true);

    // Log the error for debugging
    error_log("Registration error: " . $e->getMessage());

    sendResponse(false, "Ralat semasa pendaftaran: " . $e->getMessage());
}
?>
