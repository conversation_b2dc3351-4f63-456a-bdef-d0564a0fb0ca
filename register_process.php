<?php
require 'db.php';
require 'send_email.php';

$full_name = $_POST['full_name'];
$email = $_POST['email'];
$phone_number = $_POST['phone_number'];
$address = $_POST['address'];
$password = $_POST['password'];
$confirm_password = $_POST['confirm_password'];

if ($password !== $confirm_password) {
    die("Kata laluan tidak sepadan.");
}

if (!preg_match('/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_]).{8,}$/', $password)) {
    die("Kata laluan tidak ikut polisi.");
}

$password_hash = password_hash($password, PASSWORD_DEFAULT);
$role_id = 2;
$verify_token = bin2hex(random_bytes(16));
$token_created_at = date('Y-m-d H:i:s');

// Insert into users
$stmt = $conn->prepare("INSERT INTO users (email, password_hash, role_id) VALUES (?, ?, ?)");
$stmt->bind_param("ssi", $email, $password_hash, $role_id);
$stmt->execute();
$user_id = $stmt->insert_id;

// Insert into parents
$stmt = $conn->prepare("INSERT INTO parents (user_id, full_name, phone_number, address, verification_token, token_created_at, email_verified) VALUES (?, ?, ?, ?, ?, ?, 0)");
$stmt->bind_param("isssss", $user_id, $full_name, $phone_number, $address, $verify_token, $token_created_at);
$stmt->execute();

// Hantar emel
$verify_link = "http://***************/smktmi/verify.php?token=$verify_token";
sendVerificationEmail($email, $verify_link);

echo "Pendaftaran berjaya! Sila semak emel anda untuk pengesahan.";
?>
