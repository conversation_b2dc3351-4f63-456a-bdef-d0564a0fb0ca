<?php
/**
 * Basic test to ensure <PERSON><PERSON> is working and verify.php is accessible
 */

echo "<h1>Basic PHP Test</h1>";

echo "<h2>1. PHP Information</h2>";
echo "PHP Version: " . phpversion() . "<br>";
echo "Current Time: " . date('Y-m-d H:i:s') . "<br>";
echo "Current Directory: " . getcwd() . "<br>";

echo "<h2>2. File System Test</h2>";
$files_to_check = ['verify.php', 'register_process.php', 'db.php', 'send_email.php'];
foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        echo "✅ $file exists<br>";
    } else {
        echo "❌ $file missing<br>";
    }
}

echo "<h2>3. URL Test</h2>";
echo "Current URL: " . $_SERVER['REQUEST_URI'] . "<br>";
echo "Host: " . $_SERVER['HTTP_HOST'] . "<br>";

echo "<h2>4. Direct Links</h2>";
echo "<a href='verify.php'>Test verify.php (should show error)</a><br>";
echo "<a href='verify.php?token=test123'>Test verify.php with invalid token</a><br>";

if (isset($_GET['token'])) {
    echo "<h2>5. Token Test</h2>";
    echo "Received token: " . htmlspecialchars($_GET['token']) . "<br>";
    echo "Token length: " . strlen($_GET['token']) . "<br>";
    echo "Is hex: " . (ctype_xdigit($_GET['token']) ? 'Yes' : 'No') . "<br>";
}

?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h2 { color: #333; }
a { color: #007bff; }
</style>
