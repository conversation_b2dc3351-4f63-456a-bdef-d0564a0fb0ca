<?php
use P<PERSON><PERSON>ailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

require 'PHPMailer/src/Exception.php';
require 'PHPMailer/src/PHPMailer.php';
require 'PHPMailer/src/SMTP.php';

function sendVerificationEmail($to, $verify_link) {
    $mail = new PHPMailer(true);

    try {
        // Validate email address
        if (!filter_var($to, FILTER_VALIDATE_EMAIL)) {
            return ['success' => false, 'message' => 'Alamat emel tidak sah'];
        }

        // Server settings
        $mail->isSMTP();
        $mail->Host       = 'smtp.gmail.com';
        $mail->SMTPAuth   = true;
        $mail->Username   = '<EMAIL>';
        $mail->Password   = 'csop krln zhae vqxq';
        $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
        $mail->Port       = 587;

        // Enable debugging for troubleshooting (remove in production)
        // $mail->SMTPDebug = 2;
        // $mail->Debugoutput = 'error_log';

        // Recipients
        $mail->setFrom('<EMAIL>', 'Admin Sistem SMKTMI');
        $mail->addAddress($to);
        $mail->addReplyTo('<EMAIL>', 'Admin Sistem SMKTMI');

        // Content
        $mail->isHTML(true);
        $mail->Subject = 'Pengesahan Emel Akaun - SMKTMI';
        $mail->Body    = "
        <html>
        <head>
            <title>Pengesahan Emel Akaun</title>
        </head>
        <body>
            <h2>Selamat Datang ke Sistem SMKTMI</h2>
            <p>Terima kasih kerana mendaftar akaun dengan kami.</p>
            <p>Sila klik pautan di bawah untuk mengesahkan alamat emel anda:</p>
            <p><a href='$verify_link' style='background-color: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Sahkan Emel</a></p>
            <p>Atau salin dan tampal pautan ini ke dalam pelayar anda:</p>
            <p>$verify_link</p>
            <p><strong>Nota:</strong> Pautan ini akan tamat tempoh dalam masa 24 jam.</p>
            <p>Jika anda tidak membuat permintaan ini, sila abaikan emel ini.</p>
            <br>
            <p>Terima kasih,<br>Pasukan Admin SMKTMI</p>
        </body>
        </html>";

        $mail->AltBody = "Selamat Datang ke Sistem SMKTMI\n\n" .
                        "Terima kasih kerana mendaftar akaun dengan kami.\n" .
                        "Sila klik pautan berikut untuk mengesahkan alamat emel anda:\n\n" .
                        "$verify_link\n\n" .
                        "Nota: Pautan ini akan tamat tempoh dalam masa 24 jam.\n" .
                        "Jika anda tidak membuat permintaan ini, sila abaikan emel ini.\n\n" .
                        "Terima kasih,\nPasukan Admin SMKTMI";

        $result = $mail->send();

        if ($result) {
            return ['success' => true, 'message' => 'Emel pengesahan berjaya dihantar'];
        } else {
            return ['success' => false, 'message' => 'Gagal menghantar emel: ' . $mail->ErrorInfo];
        }

    } catch (Exception $e) {
        // Log the error for debugging
        error_log("Email sending error: " . $e->getMessage());
        return ['success' => false, 'message' => $e->getMessage()];
    }
}
?>
