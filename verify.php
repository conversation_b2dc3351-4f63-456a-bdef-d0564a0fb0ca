<?php
require 'db.php';

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set timezone to ensure consistent time handling
date_default_timezone_set('Asia/Kuala_Lumpur');

// Function to display error page
function showError($message, $details = '') {
    ?>
    <!DOCTYPE html>
    <html lang="ms">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Ralat Pengesahan - SMKTMI</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; background-color: #f5f5f5; }
            .container { max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            .error { color: #d32f2f; }
            .btn { display: inline-block; padding: 10px 20px; background-color: #1976d2; color: white; text-decoration: none; border-radius: 5px; margin-top: 20px; }
            .btn:hover { background-color: #1565c0; }
        </style>
    </head>
    <body>
        <div class="container">
            <h2 class="error">Ralat Pengesahan Emel</h2>
            <p><?php echo htmlspecialchars($message); ?></p>
            <?php if ($details): ?>
                <p><small>Butiran: <?php echo htmlspecialchars($details); ?></small></p>
            <?php endif; ?>
            <a href="login.php" class="btn">Kembali ke Log Masuk</a>
        </div>
    </body>
    </html>
    <?php
    exit;
}

// Function to display success page
function showSuccess() {
    ?>
    <!DOCTYPE html>
    <html lang="ms">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Pengesahan Berjaya - SMKTMI</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; background-color: #f5f5f5; }
            .container { max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            .success { color: #388e3c; }
            .btn { display: inline-block; padding: 10px 20px; background-color: #4caf50; color: white; text-decoration: none; border-radius: 5px; margin-top: 20px; }
            .btn:hover { background-color: #45a049; }
        </style>
    </head>
    <body>
        <div class="container">
            <h2 class="success">✓ Pengesahan Emel Berjaya</h2>
            <p>Akaun anda telah berjaya disahkan. Anda kini boleh log masuk ke sistem.</p>
            <a href="login.php" class="btn">Log Masuk Sekarang</a>
        </div>
    </body>
    </html>
    <?php
    exit;
}

// Validate token parameter
if (!isset($_GET['token']) || empty($_GET['token'])) {
    showError("Token pengesahan tidak dijumpai.", "Sila pastikan anda menggunakan pautan penuh dari emel pengesahan.");
}

$token = trim($_GET['token']);

// Validate token format (should be 32 hex characters)
if (!preg_match('/^[a-f0-9]{32}$/i', $token)) {
    showError("Format token tidak sah.", "Token pengesahan mesti mengandungi 32 aksara heksadesimal.");
}

try {
    // Get current time with timezone
    $current_time = new DateTime('now', new DateTimeZone('Asia/Kuala_Lumpur'));
    $token_valid_duration = new DateInterval('PT24H'); // 24 hours

    // Search for the token in parents table
    $stmt = $conn->prepare("SELECT parent_id, token_created_at, email_verified FROM parents WHERE verification_token = ?");
    if (!$stmt) {
        throw new Exception("Database prepare failed: " . $conn->error);
    }

    $stmt->bind_param("s", $token);
    if (!$stmt->execute()) {
        throw new Exception("Database execute failed: " . $stmt->error);
    }

    $result = $stmt->get_result();
    $row = $result->fetch_assoc();
    $stmt->close();

    if (!$row) {
        showError("Token pengesahan tidak sah atau telah digunakan.", "Sila pastikan anda menggunakan pautan terkini dari emel pengesahan.");
    }

    // Check if already verified
    if ($row['email_verified'] == 1) {
        showError("Akaun ini telah disahkan sebelum ini.", "Anda boleh terus log masuk ke sistem.");
    }

    // Check token expiration
    $token_created_at = new DateTime($row['token_created_at'], new DateTimeZone('Asia/Kuala_Lumpur'));
    $token_expiry = clone $token_created_at;
    $token_expiry->add($token_valid_duration);

    if ($current_time > $token_expiry) {
        $hours_expired = $current_time->diff($token_expiry)->h;
        showError("Token pengesahan telah tamat tempoh.", "Token ini telah tamat tempoh sejak $hours_expired jam yang lalu. Sila daftar semula atau hubungi pentadbir.");
    }

    // Start transaction for verification update
    $conn->autocommit(false);

    // Update verification status
    $updateStmt = $conn->prepare("UPDATE parents SET email_verified = 1, verification_token = NULL, token_created_at = NULL WHERE parent_id = ?");
    if (!$updateStmt) {
        throw new Exception("Update prepare failed: " . $conn->error);
    }

    $updateStmt->bind_param("i", $row['parent_id']);
    if (!$updateStmt->execute()) {
        throw new Exception("Update execute failed: " . $updateStmt->error);
    }

    if ($updateStmt->affected_rows === 0) {
        throw new Exception("No rows were updated during verification");
    }

    $updateStmt->close();

    // Commit the transaction
    $conn->commit();
    $conn->autocommit(true);

    // Log successful verification
    error_log("Email verification successful for parent_id: " . $row['parent_id']);

    // Show success page
    showSuccess();

} catch (Exception $e) {
    // Rollback transaction on error
    $conn->rollback();
    $conn->autocommit(true);

    // Log the error
    error_log("Email verification error: " . $e->getMessage());

    showError("Ralat sistem semasa pengesahan.", $e->getMessage());
}
?>
