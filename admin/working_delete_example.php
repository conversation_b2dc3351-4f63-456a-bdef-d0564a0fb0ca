<?php
session_start();

if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'Admin') {
    header("Location: ../login.php");
    exit;
}

require '../db.php';

// Generate CSRF token
if (empty($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

$message = "";

// Handle delete operation
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'delete') {
    if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
        $message = "❌ Invalid CSRF token.";
    } else {
        $teacher_id = isset($_POST['teacher_id']) ? (int)$_POST['teacher_id'] : 0;
        $subject_id = isset($_POST['subject_id']) ? (int)$_POST['subject_id'] : 0;
        $classroom_id = isset($_POST['classroom_id']) ? (int)$_POST['classroom_id'] : 0;
        
        if ($teacher_id > 0 && $subject_id > 0 && $classroom_id > 0) {
            $delete = $conn->prepare("DELETE FROM teacher_subject_classrooms WHERE teacher_id = ? AND subject_id = ? AND classroom_id = ?");
            $delete->bind_param("iii", $teacher_id, $subject_id, $classroom_id);
            
            if ($delete->execute()) {
                if ($delete->affected_rows > 0) {
                    $message = "✅ Tetapan guru berjaya dipadam.";
                } else {
                    $message = "⚠️ Tiada tetapan dijumpai untuk dipadam.";
                }
            } else {
                $message = "❌ Ralat semasa memadam tetapan guru: " . $delete->error;
            }
            $delete->close();
        } else {
            $message = "❌ Data tidak mencukupi untuk memadam tetapan.";
        }
    }
}

// Get assignments
$assignments = $conn->query("
    SELECT tsc.teacher_id, tsc.subject_id, tsc.classroom_id,
           t.full_name, s.subject_name, c.class_name
    FROM teacher_subject_classrooms tsc
    JOIN teachers t ON tsc.teacher_id = t.teacher_id
    JOIN subjects s ON tsc.subject_id = s.subject_id
    JOIN classrooms c ON tsc.classroom_id = c.classroom_id
    ORDER BY s.subject_name, c.class_name
");

$grouped = [];
if ($assignments && $assignments->num_rows > 0) {
    while ($row = $assignments->fetch_assoc()) {
        $subject = $row['subject_name'];
        if (!isset($grouped[$subject])) {
            $grouped[$subject] = [];
        }
        $grouped[$subject][] = $row;
    }
}
?>

<!DOCTYPE html>
<html lang="ms">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Working Delete Example - SMKTMI</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 1000px; margin: 0 auto; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .btn { padding: 4px 8px; margin: 2px; border: none; border-radius: 3px; cursor: pointer; font-size: 12px; }
        .btn-danger { background-color: #dc3545; color: white; }
        .btn-danger:hover { background-color: #c82333; }
        .subject-header td { background-color: rgb(108, 165, 223) !important; color: white !important; font-weight: bold; }
        .message { padding: 15px; margin: 20px 0; border-radius: 4px; background-color: #f8f9fa; border: 1px solid #dee2e6; }
    </style>
</head>
<body>

<div class="container">
    <h2>🔧 Working Delete Example</h2>
    
    <?php if ($message): ?>
        <div class="message"><?= htmlspecialchars($message) ?></div>
    <?php endif; ?>
    
    <h3>Teacher-Subject-Classroom Assignments</h3>
    
    <table>
        <thead>
            <tr>
                <th>Subjek</th>
                <th>Kelas</th>
                <th>Guru</th>
                <th>Tindakan</th>
            </tr>
        </thead>
        <tbody>
            <?php if (!empty($grouped)): ?>
                <?php foreach ($grouped as $subject => $assignments): ?>
                    <tr class="subject-header">
                        <td colspan="4"><?= htmlspecialchars($subject) ?></td>
                    </tr>
                    <?php foreach ($assignments as $assign): ?>
                        <tr>
                            <td></td>
                            <td><?= htmlspecialchars($assign['class_name']) ?></td>
                            <td><?= htmlspecialchars($assign['full_name']) ?></td>
                            <td>
                                <button type="button" class="btn btn-danger" 
                                        onclick="deleteAssignment(<?= $assign['teacher_id'] ?>, <?= $assign['subject_id'] ?>, <?= $assign['classroom_id'] ?>, '<?= htmlspecialchars($assign['full_name']) ?>', '<?= htmlspecialchars($assign['class_name']) ?>', '<?= htmlspecialchars($subject) ?>')">
                                    <i class="fas fa-trash"></i> Padam
                                </button>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php endforeach; ?>
            <?php else: ?>
                <tr><td colspan="4" style="text-align: center;">Tiada tetapan lagi.</td></tr>
            <?php endif; ?>
        </tbody>
    </table>
    
    <p><a href="assign_teacher_subject_classroom.php">← Back to Main Assignment Page</a></p>
</div>

<!-- Delete Form (hidden) -->
<form id="deleteForm" method="POST" style="display: none;">
    <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($_SESSION['csrf_token']) ?>">
    <input type="hidden" name="action" value="delete">
    <input type="hidden" name="teacher_id" id="delete_teacher_id">
    <input type="hidden" name="subject_id" id="delete_subject_id">
    <input type="hidden" name="classroom_id" id="delete_classroom_id">
</form>

<script>
function deleteAssignment(teacherId, subjectId, classroomId, teacherName, className, subjectName) {
    console.log('deleteAssignment called:', {teacherId, subjectId, classroomId, teacherName, className, subjectName});
    
    const confirmMessage = `Adakah anda pasti ingin memadam tetapan ini?\n\nGuru: ${teacherName}\nKelas: ${className}\nSubjek: ${subjectName}\n\nTindakan ini tidak boleh dibatalkan.`;
    
    if (confirm(confirmMessage)) {
        console.log('User confirmed deletion');
        
        // Set form values
        document.getElementById('delete_teacher_id').value = teacherId;
        document.getElementById('delete_subject_id').value = subjectId;
        document.getElementById('delete_classroom_id').value = classroomId;
        
        console.log('Form values set, submitting...');
        
        // Submit form
        document.getElementById('deleteForm').submit();
    } else {
        console.log('User cancelled deletion');
    }
}
</script>

</body>
</html>
