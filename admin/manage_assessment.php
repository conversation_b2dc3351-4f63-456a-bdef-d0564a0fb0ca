<?php
session_start();

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'Admin') {
    header("Location: ../login.php");
    exit;
}

require '../db.php';

$message = "";
$edit_mode = false;
$edit_assessment = null;

// Check if we're in edit group mode
$edit_group_subjects = [];
if (isset($_GET['edit_group'])) {
    $edit_mode = true;
    $edit_assessment_type = $_GET['edit_group'];
    $edit_assessment_date = $_GET['assessment_date'];
    $edit_classroom_id = (int)$_GET['classroom_id'];

    // Get the assessment group info
    $edit_stmt = $conn->prepare("
        SELECT assessment_type, assessment_date, classroom_id
        FROM assessment
        WHERE assessment_type = ? AND assessment_date = ? AND classroom_id = ?
        LIMIT 1
    ");
    $edit_stmt->bind_param("ssi", $edit_assessment_type, $edit_assessment_date, $edit_classroom_id);
    $edit_stmt->execute();
    $edit_result = $edit_stmt->get_result();
    $edit_assessment = $edit_result->fetch_assoc();
    $edit_stmt->close();

    if ($edit_assessment) {
        // Get current subjects for this assessment group
        $subjects_stmt = $conn->prepare("
            SELECT subject_id
            FROM assessment
            WHERE assessment_type = ? AND assessment_date = ? AND classroom_id = ?
        ");
        $subjects_stmt->bind_param("ssi", $edit_assessment_type, $edit_assessment_date, $edit_classroom_id);
        $subjects_stmt->execute();
        $subjects_result = $subjects_stmt->get_result();
        while ($row = $subjects_result->fetch_assoc()) {
            $edit_group_subjects[] = $row['subject_id'];
        }
        $subjects_stmt->close();
    } else {
        $message = "❌ Penilaian tidak dijumpai.";
        $edit_mode = false;
    }
}

// Handle form submission (both add and edit)
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['assessment_type'])) {
    $assessment_type = trim($_POST['assessment_type']);
    $assessment_date = $_POST['assessment_date'];
    $classroom_id = (int)$_POST['classroom_id'];
    $selected_subjects = isset($_POST['subjects']) ? $_POST['subjects'] : [];
    $assessment_id = isset($_POST['assessment_id']) ? (int)$_POST['assessment_id'] : 0;

    // Validate input
    if (empty($assessment_type)) {
        $message = "❌ Jenis penilaian tidak boleh kosong.";
    } elseif (empty($assessment_date)) {
        $message = "❌ Sila pilih tarikh penilaian.";
    } elseif ($classroom_id <= 0) {
        $message = "❌ Sila pilih kelas.";
    } elseif (empty($selected_subjects)) {
        $message = "❌ Sila pilih sekurang-kurangnya satu subjek.";
    } else {
        // Optional: Check if subject and classroom combination exists in teacher assignments
        // Comment out this check if you want to allow assessments without teacher assignments
        /*
        $check_assignment = $conn->prepare("SELECT COUNT(*) as count FROM teacher_subject_classroom WHERE subject_id = ? AND classroom_id = ?");
        $check_assignment->bind_param("ii", $subject_id, $classroom_id);
        $check_assignment->execute();
        $assignment_result = $check_assignment->get_result();
        $assignment_row = $assignment_result->fetch_assoc();
        $check_assignment->close();

        if ($assignment_row['count'] == 0) {
            $message = "❌ Tiada guru yang ditetapkan untuk kombinasi subjek dan kelas ini.";
        } else {
        */
            if (isset($_POST['edit_group']) && $_POST['edit_group'] == '1') {
                // Edit group mode - update subjects for assessment group
                $original_assessment_type = $_POST['original_assessment_type'];
                $original_assessment_date = $_POST['original_assessment_date'];
                $original_classroom_id = (int)$_POST['original_classroom_id'];

                // Delete existing assessments for this group
                $delete_stmt = $conn->prepare("DELETE FROM assessment WHERE assessment_type = ? AND assessment_date = ? AND classroom_id = ?");
                $delete_stmt->bind_param("ssi", $original_assessment_type, $original_assessment_date, $original_classroom_id);
                $delete_stmt->execute();
                $delete_stmt->close();

                // Insert new assessments with updated subjects
                $success_count = 0;
                foreach ($selected_subjects as $subject_id) {
                    $subject_id = (int)$subject_id;
                    $stmt = $conn->prepare("INSERT INTO assessment (assessment_type, assessment_date, subject_id, classroom_id) VALUES (?, ?, ?, ?)");
                    $stmt->bind_param("ssii", $assessment_type, $assessment_date, $subject_id, $classroom_id);
                    if ($stmt->execute()) {
                        $success_count++;
                    }
                    $stmt->close();
                }

                if ($success_count > 0) {
                    header("Location: manage_assessment.php?success=2&count=" . $success_count);
                    exit;
                } else {
                    $message = "❌ Ralat semasa mengemaskini penilaian.";
                }
            } else {
                // Add mode - insert new assessments for each selected subject
                $success_count = 0;
                $duplicate_count = 0;

                foreach ($selected_subjects as $subject_id) {
                    $subject_id = (int)$subject_id;

                    // Check for duplicate assessment type, subject, and classroom
                    $check_stmt = $conn->prepare("SELECT assessment_id FROM assessment WHERE assessment_type = ? AND subject_id = ? AND classroom_id = ?");
                    $check_stmt->bind_param("sii", $assessment_type, $subject_id, $classroom_id);
                    $check_stmt->execute();
                    $check_result = $check_stmt->get_result();

                    if ($check_result->num_rows > 0) {
                        $duplicate_count++;
                    } else {
                        $stmt = $conn->prepare("INSERT INTO assessment (assessment_type, assessment_date, subject_id, classroom_id) VALUES (?, ?, ?, ?)");
                        $stmt->bind_param("ssii", $assessment_type, $assessment_date, $subject_id, $classroom_id);

                        if ($stmt->execute()) {
                            $success_count++;
                        }
                        $stmt->close();
                    }
                    $check_stmt->close();
                }

                if ($success_count > 0) {
                    $message = "✅ {$success_count} penilaian berjaya ditambah.";
                    if ($duplicate_count > 0) {
                        $message .= " {$duplicate_count} penilaian diabaikan (sudah wujud).";
                    }
                    header("Location: manage_assessment.php?success=1&msg=" . urlencode($message));
                    exit;
                } else {
                    $message = "❌ Tiada penilaian ditambah. Semua kombinasi sudah wujud.";
                }
            }
        // }  // End of teacher assignment check
    }
}

// Handle delete request
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $delete_id = (int)$_GET['delete'];

    // Check if assessment has results
    $check_stmt = $conn->prepare("SELECT COUNT(*) as count FROM assessment_result WHERE assessment_id = ?");
    $check_stmt->bind_param("i", $delete_id);
    $check_stmt->execute();
    $check_result = $check_stmt->get_result();
    $check_row = $check_result->fetch_assoc();
    $check_stmt->close();

    if ($check_row['count'] > 0) {
        $message = "❌ Tidak boleh padam penilaian yang sudah mempunyai keputusan.";
    } else {
        $delete_stmt = $conn->prepare("DELETE FROM assessment WHERE assessment_id = ?");
        $delete_stmt->bind_param("i", $delete_id);

        if ($delete_stmt->execute()) {
            header("Location: manage_assessment.php?success=3");
            exit;
        } else {
            $message = "❌ Ralat padam: " . $delete_stmt->error;
        }
        $delete_stmt->close();
    }
}

// Handle delete group request
if (isset($_GET['delete_group'])) {
    $assessment_type = $_GET['delete_group'];
    $assessment_date = $_GET['assessment_date'];
    $classroom_id = (int)$_GET['classroom_id'];

    // Delete all assessment results first (if any)
    $delete_results_stmt = $conn->prepare("
        DELETE ar FROM assessment_result ar
        JOIN assessment a ON ar.assessment_id = a.assessment_id
        WHERE a.assessment_type = ? AND a.assessment_date = ? AND a.classroom_id = ?
    ");
    $delete_results_stmt->bind_param("ssi", $assessment_type, $assessment_date, $classroom_id);
    $delete_results_stmt->execute();
    $delete_results_stmt->close();

    // Then delete the assessments
    $stmt = $conn->prepare("DELETE FROM assessment WHERE assessment_type = ? AND assessment_date = ? AND classroom_id = ?");
    $stmt->bind_param("ssi", $assessment_type, $assessment_date, $classroom_id);

    if ($stmt->execute()) {
        $deleted_count = $stmt->affected_rows;
        header("Location: manage_assessment.php?success=3&count=" . $deleted_count);
        exit;
    } else {
        $message = "❌ Ralat semasa memadam: " . $stmt->error;
    }
    $stmt->close();
}

// Pagination setup
$limit = 10;
$page = isset($_GET['page']) && is_numeric($_GET['page']) && $_GET['page'] > 0 ? (int) $_GET['page'] : 1;

// Pagination logic for edit mode is simplified since we're using group-based editing
// No need to find specific page for individual assessment records

$offset = ($page - 1) * $limit;

// Check if assessment table exists
$table_check = $conn->query("SHOW TABLES LIKE 'assessment'");
if (!$table_check || $table_check->num_rows == 0) {
    // Redirect to setup page if tables don't exist
    header("Location: setup_assessment_tables.php");
    exit;
}

// Count total assessment groups
$total_result = $conn->query("
    SELECT COUNT(*) AS total
    FROM (
        SELECT a.assessment_type, a.assessment_date, a.classroom_id
        FROM assessment a
        GROUP BY a.assessment_type, a.assessment_date, a.classroom_id
    ) AS grouped_assessments
");
if (!$total_result) {
    die("Error counting assessments: " . $conn->error);
}
$total_row = $total_result->fetch_assoc();
$total_assessments = $total_row['total'];
$total_pages = ceil($total_assessments / $limit);

// Ensure page is within valid range
if ($page > $total_pages && $total_pages > 0) {
    $page = $total_pages;
    $offset = ($page - 1) * $limit;
}

// Fetch assessments grouped by type and classroom
$assessments = [];
$sql = "SELECT a.assessment_type, a.assessment_date, a.classroom_id, c.class_name,
               GROUP_CONCAT(DISTINCT a.assessment_id) as assessment_ids,
               COUNT(DISTINCT a.subject_id) as subject_count,
               SUM(CASE WHEN ar.assessment_id IS NOT NULL THEN 1 ELSE 0 END) as result_count
        FROM assessment a
        LEFT JOIN classrooms c ON a.classroom_id = c.classroom_id
        LEFT JOIN assessment_result ar ON a.assessment_id = ar.assessment_id
        GROUP BY a.assessment_type, a.assessment_date, a.classroom_id
        ORDER BY a.assessment_date DESC, a.assessment_type, c.class_name
        LIMIT ? OFFSET ?";
$stmt = $conn->prepare($sql);
if (!$stmt) {
    die("Error preparing statement: " . $conn->error);
}
$stmt->bind_param("ii", $limit, $offset);
$stmt->execute();
$result = $stmt->get_result();
while ($row = $result->fetch_assoc()) {
    $assessments[] = $row;
}
$stmt->close();

// Fetch subjects for dropdown
$subjects = [];
$subject_result = $conn->query("SELECT subject_id, subject_name FROM subjects ORDER BY subject_name");
if ($subject_result) {
    while ($row = $subject_result->fetch_assoc()) {
        $subjects[] = $row;
    }
} else {
    die("Error fetching subjects: " . $conn->error);
}

// Fetch classrooms for dropdown
$classrooms = [];
$classroom_result = $conn->query("SELECT classroom_id, class_name FROM classrooms ORDER BY class_name");
if ($classroom_result) {
    while ($row = $classroom_result->fetch_assoc()) {
        $classrooms[] = $row;
    }
} else {
    die("Error fetching classrooms: " . $conn->error);
}

// Fetch subjects assigned to each classroom
$classroom_subjects = [];
$classroom_subjects_query = "
    SELECT DISTINCT tsc.classroom_id, tsc.subject_id, s.subject_name
    FROM teacher_subject_classrooms tsc
    JOIN subjects s ON tsc.subject_id = s.subject_id
    ORDER BY tsc.classroom_id, s.subject_name
";
$classroom_subjects_result = $conn->query($classroom_subjects_query);
if ($classroom_subjects_result) {
    while ($row = $classroom_subjects_result->fetch_assoc()) {
        $classroom_subjects[$row['classroom_id']][] = $row;
    }
}
?>

<!DOCTYPE html>
<html lang="ms">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pengurusan Penilaian - SMKTMI</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>

<style>
/* Sidebar Styling */
.sidebar {
    position: fixed;
    top: 0; left: 0;
    width: 300px;
    height: 100vh;
    background-color: #2c3e50;
    padding: 30px 20px;
    color: #ecf0f1;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    overflow-y: auto;
    box-shadow: 5px 0 15px rgba(0,0,0,0.4);
    border-right: 1px solid #34495e;
}

.sidebar h4 {
    font-size: 18px;
    font-weight: 700;
    text-transform: uppercase;
    margin: 30px 0 12px;
    border-bottom: 2px solid #3498db;
    padding-bottom: 6px;
    letter-spacing: 1.5px;
}

.sidebar a {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    color: #bdc3c7;
    text-decoration: none;
    font-size: 16px;
    margin-bottom: 8px;
    border-radius: 6px;
    transition: background-color 0.3s ease, color 0.3s ease;
}

.sidebar a i {
    margin-right: 12px;
    width: 18px;
    text-align: center;
}

.sidebar a:hover {
    background-color: #2980b9;
    color: #ecf0f1;
}

/* Content Styling */
.content {
    margin-left: 280px;
    padding: 40px 30px;
    min-height: 100vh;
    background-color: #f9f9f9;
}

.form-container {
    width: 100%;
    max-width: 1200px;
    background-color: #fff;
    padding: 30px 40px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0 auto;
}

.form-container h2 {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 25px;
    border-bottom: 3px solid #3498db;
    padding-bottom: 10px;
    color: #2980b9;
    text-align: center;
}

.alert {
    padding: 15px;
    background-color: #d9edf7;
    border: 1px solid #bce8f1;
    border-radius: 5px;
    color: #31708f;
    margin-bottom: 25px;
    text-align: center;
}

.alert-success {
    background-color: #dff0d8;
    border-color: #d6e9c6;
    color: #3c763d;
}

.alert-danger {
    background-color: #f2dede;
    border-color: #ebccd1;
    color: #a94442;
}

/* Form Styling */
.form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.subjects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
    margin-top: 10px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 5px;
    border: 1px solid #dee2e6;
}

.subject-checkbox {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    background-color: white;
    border-radius: 4px;
    border: 1px solid #ddd;
    transition: background-color 0.2s ease;
}

.subject-checkbox:hover {
    background-color: #e9ecef;
}

.subject-checkbox input[type="checkbox"] {
    margin-right: 8px;
    transform: scale(1.2);
}

.subject-checkbox label {
    margin: 0;
    cursor: pointer;
    font-weight: 500;
}

.form-group {
    margin-bottom: 15px;
}

label {
    font-weight: 700;
    margin-bottom: 6px;
    display: block;
    color: #34495e;
}

input[type="text"], input[type="date"], select {
    width: 100%;
    padding: 10px 14px;
    font-size: 16px;
    border: 1.5px solid #ccc;
    border-radius: 6px;
    transition: border-color 0.3s ease;
}

input:focus, select:focus {
    border-color: #2980b9;
    outline: none;
}

button[type="submit"] {
    padding: 12px 30px;
    background-color: #2980b9;
    border: none;
    color: #ecf0f1;
    font-size: 16px;
    font-weight: 700;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

button[type="submit"]:hover {
    background-color: #1a5d8f;
}

/* Table Styling */
.table {
    margin-top: 30px;
}

.table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #2c3e50;
}

.table tr.editing {
    background-color: #fff3cd !important;
    border-left: 4px solid #f39c12;
}

.table tr.editing td {
    font-weight: 600;
}

.action-btn {
    padding: 6px 12px;
    color: white;
    text-decoration: none;
    border-radius: 4px;
    font-size: 14px;
    display: inline-block;
    transition: all 0.3s ease;
    margin-right: 5px;
}

.action-btn:hover {
    color: white;
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

.btn-edit {
    background-color: #3498db;
}

.btn-edit:hover {
    background-color: #2980b9;
}

.btn-delete {
    background-color: #e74c3c;
}

.btn-delete:hover {
    background-color: #c0392b;
}

.btn-cancel {
    background-color: #95a5a6;
}

.btn-cancel:hover {
    background-color: #7f8c8d;
}

.btn-marks {
    background-color: #27ae60;
}

.btn-marks:hover {
    background-color: #229954;
}

.action-buttons {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}

.action-buttons .action-btn {
    margin-right: 0;
}

.badge {
    padding: 6px 10px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.bg-success {
    background-color: #27ae60 !important;
    color: white;
}

.bg-secondary {
    background-color: #95a5a6 !important;
    color: white;
}

.table td {
    vertical-align: middle;
}

.table th {
    text-align: center;
    vertical-align: middle;
}

.table td:last-child {
    text-align: center;
}

.table td:nth-child(6) {
    text-align: center;
}

/* Responsive */
@media (max-width: 768px) {
    .sidebar {
        width: 100%;
        height: auto;
        position: relative;
        box-shadow: none;
    }
    .content {
        margin-left: 0;
        padding: 20px;
    }
    .form-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<div class="sidebar">
    <h4>Menu Admin</h4>
    <a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>

    <h4>Pengurusan Guru</h4>
    <a href="register_teacher.php"><i class="fas fa-user-plus"></i> Daftar Guru</a>
    <a href="list_teachers.php"><i class="fas fa-user-edit"></i> Kemaskini Maklumat Guru</a>

    <h4>Pengurusan Kelas</h4>
    <a href="add_classroom.php"><i class="fas fa-chalkboard"></i> Daftar Kelas</a>
    <a href="assign_teacher.php"><i class="fas fa-user-tie"></i> Tetapan Guru Kelas</a>

    <h4>Pengurusan Subjek</h4>
    <a href="add_subject.php"><i class="fas fa-book"></i> Daftar Subjek</a>
    <a href="assign_teacher_subject_classroom.php"><i class="fas fa-tasks"></i> Tetapan Guru-Subjek-Kelas</a>

    <h4>Pengurusan Penilaian</h4>
    <a href="manage_assessment.php"><i class="fas fa-clipboard-list"></i> Urus Penilaian</a>

    <h4>Pengurusan Jadual</h4>
    <a href="manage_schedules.php"><i class="fas fa-calendar-alt"></i> Urus Jadual Waktu</a>

    <h4>Pengurusan Disiplin</h4>
    <a href="manage_discipline.php"><i class="fas fa-exclamation-triangle"></i> Urus Rekod Disiplin</a>

    <h4>Log Sistem</h4>
    <a href="view_admin_logs.php"><i class="fas fa-history"></i> Log Aktiviti Sistem</a>

    <h4>Sistem</h4>
    <a href="change_password.php"><i class="fas fa-shield-alt"></i> Tukar Kata Laluan</a>
    <a href="logout.php"><i class="fas fa-sign-out-alt"></i> Log Keluar</a>
</div>

<div class="content">
  <div class="form-container">
    <h2>Pengurusan Penilaian</h2>

    <?php if (isset($_GET['success'])): ?>
      <?php if ($_GET['success'] == '1'): ?>
        <div class="alert alert-success">✅ Penilaian berjaya ditambah.</div>
      <?php elseif ($_GET['success'] == '2'): ?>
        <div class="alert alert-success">✅ Penilaian berjaya dikemaskini.</div>
      <?php elseif ($_GET['success'] == '3'): ?>
        <div class="alert alert-success">✅ Penilaian berjaya dipadam.</div>
      <?php endif; ?>
    <?php elseif (!empty($message)): ?>
      <div class="alert alert-danger"><?php echo htmlspecialchars($message); ?></div>
    <?php endif; ?>

    <!-- Search/Filter Section -->
    <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 30px;">
      <h5 style="margin-bottom: 15px; color: #2c3e50;"><i class="fas fa-search"></i> Cari Penilaian</h5>
      <div class="form-grid">
        <div class="form-group">
          <label for="search_type">Jenis Penilaian</label>
          <input type="text" id="search_type" placeholder="Cari mengikut jenis..." onkeyup="filterTable()">
        </div>
        <div class="form-group">
          <label for="search_subject">Subjek</label>
          <select id="search_subject" onchange="filterTable()">
            <option value="">Semua Subjek</option>
            <?php foreach ($subjects as $subject): ?>
              <option value="<?php echo htmlspecialchars($subject['subject_name']); ?>">
                <?php echo htmlspecialchars($subject['subject_name']); ?>
              </option>
            <?php endforeach; ?>
          </select>
        </div>
      </div>
      <div style="text-align: center; margin-top: 15px;">
        <button type="button" onclick="clearSearch()" style="background-color: #95a5a6; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
          <i class="fas fa-times"></i> Kosongkan Carian
        </button>
        <span id="search-results" style="margin-left: 15px; color: #7f8c8d; font-style: italic;"></span>
      </div>
    </div>

    <!-- Add/Edit Assessment Form -->
    <h4 style="margin-top: 30px; margin-bottom: 20px; text-align: center; color: #2c3e50;">
      <?php echo $edit_mode ? 'Kemaskini Penilaian' : 'Tambah Penilaian Baharu'; ?>
    </h4>

    <form method="post">
      <?php if ($edit_mode && $edit_assessment): ?>
        <input type="hidden" name="edit_group" value="1">
        <input type="hidden" name="original_assessment_type" value="<?php echo htmlspecialchars($edit_assessment['assessment_type']); ?>">
        <input type="hidden" name="original_assessment_date" value="<?php echo $edit_assessment['assessment_date']; ?>">
        <input type="hidden" name="original_classroom_id" value="<?php echo $edit_assessment['classroom_id']; ?>">
      <?php endif; ?>

      <div class="form-grid">
        <div class="form-group">
          <label for="assessment_type">Jenis Penilaian</label>
          <input type="text" name="assessment_type" id="assessment_type" required
            placeholder="Contoh: Ujian 1, Peperiksaan Pertengahan Tahun"
            value="<?php echo $edit_mode && $edit_assessment ? htmlspecialchars($edit_assessment['assessment_type']) : (isset($_POST['assessment_type']) ? htmlspecialchars($_POST['assessment_type']) : ''); ?>">
        </div>

        <div class="form-group">
          <label for="assessment_date">Tarikh Penilaian</label>
          <input type="date" name="assessment_date" id="assessment_date" required
            value="<?php echo $edit_mode && $edit_assessment ? $edit_assessment['assessment_date'] : (isset($_POST['assessment_date']) ? $_POST['assessment_date'] : ''); ?>">
        </div>

        <div class="form-group">
          <label for="classroom_id">Kelas</label>
          <select name="classroom_id" id="classroom_id" required onchange="loadClassroomSubjects()">
            <option value="">Pilih Kelas</option>
            <?php foreach ($classrooms as $classroom): ?>
              <option value="<?php echo $classroom['classroom_id']; ?>"
                <?php echo ($edit_mode && $edit_assessment && $edit_assessment['classroom_id'] == $classroom['classroom_id']) || (isset($_POST['classroom_id']) && $_POST['classroom_id'] == $classroom['classroom_id']) ? 'selected' : ''; ?>>
                <?php echo htmlspecialchars($classroom['class_name']); ?>
              </option>
            <?php endforeach; ?>
          </select>
        </div>
      </div>

      <!-- Subject Selection -->
      <div class="form-group" id="subjects-section" style="display: none;">
        <label>Subjek untuk Kelas Terpilih:</label>
        <div id="subjects-container" class="subjects-grid">
          <!-- Subjects will be loaded here via JavaScript -->
        </div>
      </div>

      <div style="text-align: center; margin-top: 20px;">
        <button type="submit"><?php echo $edit_mode ? 'Kemaskini Penilaian' : 'Tambah Penilaian'; ?></button>
        <?php if ($edit_mode): ?>
          <a href="manage_assessment.php" class="action-btn btn-cancel" style="margin-left: 15px; padding: 12px 30px; font-weight: 700;">Batal</a>
        <?php endif; ?>
      </div>
    </form>

    <!-- Assessment List -->
    <?php if (count($assessments) > 0): ?>
      <table class="table table-bordered table-striped">
        <thead class="table-light">
          <tr>
            <th>Bil.</th>
            <th>Jenis Penilaian</th>
            <th>Tarikh</th>
            <th>Kelas</th>
            <th>Status</th>
            <th>Tindakan</th>
          </tr>
        </thead>
        <tbody>
          <?php foreach ($assessments as $index => $assessment): ?>
            <tr<?php echo ($edit_mode && $edit_assessment && $assessment['assessment_type'] == $edit_assessment['assessment_type'] && $assessment['assessment_date'] == $edit_assessment['assessment_date'] && $assessment['classroom_id'] == $edit_assessment['classroom_id']) ? ' class="editing"' : ''; ?>>
              <td><?php echo $offset + $index + 1; ?></td>
              <td><?php echo htmlspecialchars($assessment['assessment_type']); ?></td>
              <td><?php echo date('d/m/Y', strtotime($assessment['assessment_date'])); ?></td>
              <td><?php echo htmlspecialchars($assessment['class_name'] ?? 'Kelas Dipadam'); ?></td>
              <td>
                <?php if ($assessment['result_count'] > 0): ?>
                  <span class="badge bg-success">Aktif (<?php echo $assessment['subject_count']; ?> subjek)</span>
                <?php else: ?>
                  <span class="badge bg-secondary">Belum Digunakan (<?php echo $assessment['subject_count']; ?> subjek)</span>
                <?php endif; ?>
              </td>
              <td>
                <div class="action-buttons">
                  <a href="manage_assessment.php?edit_group=<?php echo urlencode($assessment['assessment_type']); ?>&assessment_date=<?php echo urlencode($assessment['assessment_date']); ?>&classroom_id=<?php echo $assessment['classroom_id']; ?>"
                     class="action-btn btn-edit" title="Edit Penilaian">
                    <i class="fas fa-edit"></i> Edit
                  </a>
                  <a href="manage_assessment.php?delete_group=<?php echo urlencode($assessment['assessment_type']); ?>&assessment_date=<?php echo urlencode($assessment['assessment_date']); ?>&classroom_id=<?php echo $assessment['classroom_id']; ?>"
                     class="action-btn btn-delete" title="Padam Penilaian"
                     onclick="return confirm('Adakah anda pasti ingin memadam semua penilaian untuk <?php echo htmlspecialchars($assessment['assessment_type']); ?> di kelas <?php echo htmlspecialchars($assessment['class_name']); ?>?\n\nAmaran: Tindakan ini tidak boleh dibatalkan.')">
                    <i class="fas fa-trash"></i> Padam
                  </a>
                </div>
              </td>
            </tr>
          <?php endforeach; ?>
        </tbody>
      </table>

      <!-- Pagination -->
      <?php if ($total_pages > 1): ?>
        <div class="pagination" style="margin-top: 30px; display: flex; justify-content: center; gap: 5px; align-items: center;">
          <?php
          // Build query string for pagination
          $query_params = [];
          if (isset($_GET['edit_group'])) {
              $query_params['edit_group'] = $_GET['edit_group'];
              $query_params['classroom_id'] = $_GET['classroom_id'];
          }
          $query_string = !empty($query_params) ? '&' . http_build_query($query_params) : '';
          ?>

          <?php if ($page > 1): ?>
            <a href="?page=<?= $page - 1 ?><?= $query_string ?>" style="padding: 8px 12px; border: 1px solid #ddd; text-decoration: none; color: #3498db; border-radius: 4px;">&laquo; Sebelumnya</a>
          <?php else: ?>
            <span style="padding: 8px 12px; border: 1px solid #ddd; color: #bdc3c7; border-radius: 4px; cursor: not-allowed;">&laquo; Sebelumnya</span>
          <?php endif; ?>

          <?php for ($i = 1; $i <= $total_pages; $i++): ?>
            <?php if ($i === $page): ?>
              <span style="padding: 8px 12px; border: 1px solid #3498db; background-color: #3498db; color: white; border-radius: 4px;"><?= $i ?></span>
            <?php else: ?>
              <a href="?page=<?= $i ?><?= $query_string ?>" style="padding: 8px 12px; border: 1px solid #ddd; text-decoration: none; color: #3498db; border-radius: 4px;"><?= $i ?></a>
            <?php endif; ?>
          <?php endfor; ?>

          <?php if ($page < $total_pages): ?>
            <a href="?page=<?= $page + 1 ?><?= $query_string ?>" style="padding: 8px 12px; border: 1px solid #ddd; text-decoration: none; color: #3498db; border-radius: 4px;">Seterusnya &raquo;</a>
          <?php else: ?>
            <span style="padding: 8px 12px; border: 1px solid #ddd; color: #bdc3c7; border-radius: 4px; cursor: not-allowed;">Seterusnya &raquo;</span>
          <?php endif; ?>
        </div>
      <?php endif; ?>
    <?php else: ?>
      <p style="text-align: center; color: #7f8c8d; font-style: italic; padding: 40px;">Tiada penilaian direkodkan.</p>
    <?php endif; ?>
  </div>
</div>

<script>
// Form validation and enhancement
// Classroom subjects data from PHP
const classroomSubjects = <?php echo json_encode($classroom_subjects); ?>;
const editGroupSubjects = <?php echo json_encode($edit_group_subjects); ?>;

function loadClassroomSubjects() {
    const classroomSelect = document.getElementById('classroom_id');
    const subjectsSection = document.getElementById('subjects-section');
    const subjectsContainer = document.getElementById('subjects-container');

    const selectedClassroom = classroomSelect.value;

    if (selectedClassroom && classroomSubjects[selectedClassroom]) {
        const subjects = classroomSubjects[selectedClassroom];
        let subjectsHtml = '';

        subjects.forEach(function(subject) {
            const isChecked = editGroupSubjects.includes(parseInt(subject.subject_id)) ? 'checked' : '';
            subjectsHtml += `
                <div class="subject-checkbox">
                    <input type="checkbox" name="subjects[]" value="${subject.subject_id}" id="subject_${subject.subject_id}" ${isChecked}>
                    <label for="subject_${subject.subject_id}">${subject.subject_name}</label>
                </div>
            `;
        });

        subjectsContainer.innerHTML = subjectsHtml;
        subjectsSection.style.display = 'block';
    } else {
        subjectsSection.style.display = 'none';
        subjectsContainer.innerHTML = '';
    }
}

document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form[method="post"]');
    const assessmentTypeInput = document.getElementById('assessment_type');
    const assessmentDateInput = document.getElementById('assessment_date');
    const classroomSelect = document.getElementById('classroom_id');

    // Load subjects if in edit mode
    if (classroomSelect.value) {
        loadClassroomSubjects();
    }

    // Form validation
    if (form) {
        form.addEventListener('submit', function(e) {
            let isValid = true;
            let errorMessage = '';

            // Validate assessment type
            if (!assessmentTypeInput.value.trim()) {
                isValid = false;
                errorMessage += '• Jenis penilaian diperlukan\n';
            }

            // Validate assessment date
            if (!assessmentDateInput.value) {
                isValid = false;
                errorMessage += '• Tarikh penilaian diperlukan\n';
            }

            // Validate classroom
            if (!classroomSelect.value) {
                isValid = false;
                errorMessage += '• Sila pilih kelas\n';
            }

            // Validate subjects
            const selectedSubjects = document.querySelectorAll('input[name="subjects[]"]:checked');
            if (selectedSubjects.length === 0) {
                isValid = false;
                errorMessage += '• Sila pilih sekurang-kurangnya satu subjek\n';
            }

            if (!isValid) {
                e.preventDefault();
                alert('Sila betulkan ralat berikut:\n\n' + errorMessage);
                return false;
            }
        });
    }

    // Auto-capitalize assessment type
    if (assessmentTypeInput) {
        assessmentTypeInput.addEventListener('input', function() {
            // Capitalize first letter of each word
            this.value = this.value.replace(/\b\w/g, function(char) {
                return char.toUpperCase();
            });
        });
    }

    // Confirmation for delete actions
    const deleteButtons = document.querySelectorAll('.btn-delete');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            const confirmed = confirm(
                'Adakah anda pasti ingin memadam penilaian ini?\n\n' +
                'AMARAN: Tindakan ini akan memadam:\n' +
                '• Maklumat penilaian\n' +
                '• Semua markah yang berkaitan\n\n' +
                'Tindakan ini tidak boleh dibatalkan!'
            );

            if (!confirmed) {
                e.preventDefault();
                return false;
            }
        });
    });
});

// Table filtering function
function filterTable() {
    const searchType = document.getElementById('search_type').value.toLowerCase();
    const searchSubject = document.getElementById('search_subject').value.toLowerCase();
    const table = document.querySelector('.table tbody');

    if (!table) return; // Exit if table not found

    const rows = table.getElementsByTagName('tr');

    for (let i = 0; i < rows.length; i++) {
        const row = rows[i];
        const cells = row.getElementsByTagName('td');

        // Table structure: Bil(0), Jenis(1), Tarikh(2), Subjek(3), Kelas(4), Markah(5), Tindakan(6)
        if (cells.length >= 4) {
            const typeCell = cells[1]; // Assessment type column
            const subjectCell = cells[3]; // Subject column

            if (typeCell && subjectCell) {
                const typeText = typeCell.textContent.toLowerCase().trim();
                const subjectText = subjectCell.textContent.toLowerCase().trim();

                const typeMatch = searchType === '' || typeText.includes(searchType);
                const subjectMatch = searchSubject === '' || subjectText.includes(searchSubject);

                if (typeMatch && subjectMatch) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            }
        }
    }

    // Update row numbers and search results
    updateRowNumbers();
    updateSearchResults();
}

// Update row numbers after filtering
function updateRowNumbers() {
    const table = document.querySelector('.table tbody');

    if (!table) return; // Exit if table not found

    const rows = table.getElementsByTagName('tr');
    let visibleCount = 1;

    for (let i = 0; i < rows.length; i++) {
        const row = rows[i];
        if (row.style.display !== 'none') {
            const cells = row.getElementsByTagName('td');
            if (cells.length > 0) {
                const numberCell = cells[0]; // First cell is the number column
                if (numberCell) {
                    numberCell.textContent = visibleCount++;
                }
            }
        }
    }
}

// Clear search function
function clearSearch() {
    document.getElementById('search_type').value = '';
    document.getElementById('search_subject').value = '';
    filterTable();
}

// Update search results count
function updateSearchResults() {
    const table = document.querySelector('.table tbody');
    const resultsSpan = document.getElementById('search-results');

    if (!table || !resultsSpan) return;

    const rows = table.getElementsByTagName('tr');
    let visibleCount = 0;
    let totalCount = rows.length;

    for (let i = 0; i < rows.length; i++) {
        if (rows[i].style.display !== 'none') {
            visibleCount++;
        }
    }

    const searchType = document.getElementById('search_type').value;
    const searchSubject = document.getElementById('search_subject').value;

    if (searchType || searchSubject) {
        resultsSpan.textContent = `Menunjukkan ${visibleCount} daripada ${totalCount} penilaian`;
    } else {
        resultsSpan.textContent = '';
    }
}

// Initialize search on page load
document.addEventListener('DOMContentLoaded', function() {
    // Initialize search results
    updateSearchResults();
});
</script>

</body>
</html>
