<?php
session_start();

if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'Admin') {
    header("Location: ../login.php");
    exit;
}

require '../db.php';

// Check if schedules table exists
$tableCheck = $conn->query("SHOW TABLES LIKE 'schedules'");
if (!$tableCheck || $tableCheck->num_rows == 0) {
    // Table doesn't exist, show error message
    $table_missing = true;
} else {
    $table_missing = false;
}

$message = "";
$success = false;

// Get current academic year
$current_year = date('Y');
$academic_year = $current_year . '/' . ($current_year + 1);

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['manage_class_schedule'])) {
        $classroom_id = (int)$_POST['classroom_id'];
        $schedules_data = $_POST['schedules'] ?? [];

        // Delete existing schedules for this class
        $deleteStmt = $conn->prepare("DELETE FROM schedules WHERE classroom_id = ? AND academic_year = ?");
        $deleteStmt->bind_param("is", $classroom_id, $academic_year);
        $deleteStmt->execute();

        // Insert new schedules
        $insertStmt = $conn->prepare("
            INSERT INTO schedules (day_name, time_slot, teacher_id, subject_id, classroom_id, academic_year)
            VALUES (?, ?, ?, ?, ?, ?)
        ");

        $added_count = 0;
        foreach ($schedules_data as $session => $session_data) {
            foreach ($session_data as $day => $time_slots) {
                foreach ($time_slots as $time_slot => $schedule_info) {
                    if (!empty($schedule_info['teacher_id']) && !empty($schedule_info['subject_id'])) {
                        $teacher_id = (int)$schedule_info['teacher_id'];
                        $subject_id = (int)$schedule_info['subject_id'];

                        $insertStmt->bind_param("ssiiss", $day, $time_slot, $teacher_id, $subject_id, $classroom_id, $academic_year);
                        if ($insertStmt->execute()) {
                            $added_count++;
                        }
                    }
                }
            }
        }

        if ($added_count > 0) {
            $message = "✅ Jadual berjaya dikemaskini. $added_count slot jadual ditambah.";
            $success = true;
        } else {
            $message = "⚠️ Tiada jadual ditambah. Sila pastikan guru dan subjek dipilih.";
        }
    }
}

// Get selected classroom
$selected_classroom_id = $_POST['classroom_id'] ?? $_GET['classroom_id'] ?? 0;

// Get all classrooms
$classrooms = [];
$classroomsQuery = $conn->query("SELECT classroom_id, class_name, tingkatan FROM classrooms ORDER BY tingkatan, class_name");
while ($classroom = $classroomsQuery->fetch_assoc()) {
    $classrooms[] = $classroom;
}

// Get all teachers
$teachers = [];
$teachersQuery = $conn->query("SELECT teacher_id, full_name FROM teachers ORDER BY full_name");
while ($teacher = $teachersQuery->fetch_assoc()) {
    $teachers[] = $teacher;
}

// Get subjects for the selected classroom (from teacher_subject_classrooms)
$subjects = [];
if ($selected_classroom_id) {
    $subjectsQuery = $conn->prepare("
        SELECT DISTINCT s.subject_id, s.subject_name, tsc.teacher_id, t.full_name as teacher_name
        FROM subjects s
        JOIN teacher_subject_classrooms tsc ON s.subject_id = tsc.subject_id
        JOIN teachers t ON tsc.teacher_id = t.teacher_id
        WHERE tsc.classroom_id = ?
        ORDER BY s.subject_name
    ");
    $subjectsQuery->bind_param("i", $selected_classroom_id);
    $subjectsQuery->execute();
    $subjectsResult = $subjectsQuery->get_result();
    while ($subject = $subjectsResult->fetch_assoc()) {
        $subjects[] = $subject;
    }
}

// Get existing schedules for selected classroom
$existing_schedules = [];
if ($selected_classroom_id) {
    $schedulesQuery = $conn->prepare("
        SELECT s.*, t.full_name as teacher_name, sub.subject_name
        FROM schedules s
        JOIN teachers t ON s.teacher_id = t.teacher_id
        JOIN subjects sub ON s.subject_id = sub.subject_id
        WHERE s.classroom_id = ? AND s.academic_year = ?
    ");
    $schedulesQuery->bind_param("is", $selected_classroom_id, $academic_year);
    $schedulesQuery->execute();
    $schedulesResult = $schedulesQuery->get_result();
    while ($schedule = $schedulesResult->fetch_assoc()) {
        // Determine session based on time slot
        $time_slot = $schedule['time_slot'];
        $session = 'morning';

        // Check if it's afternoon session
        if ($schedule['day_name'] === 'Jumaat') {
            if (strpos($time_slot, '14:') === 0 || strpos($time_slot, '15:') === 0 ||
                strpos($time_slot, '16:') === 0 || strpos($time_slot, '17:') === 0 ||
                strpos($time_slot, '18:') === 0) {
                $session = 'afternoon';
            }
        } else {
            if (strpos($time_slot, '12:') === 0 || strpos($time_slot, '13:') === 0 ||
                strpos($time_slot, '14:') === 0 || strpos($time_slot, '15:') === 0 ||
                strpos($time_slot, '16:') === 0 || strpos($time_slot, '17:') === 0 ||
                strpos($time_slot, '18:') === 0) {
                $session = 'afternoon';
            }
        }

        $existing_schedules[$session][$schedule['day_name']][$schedule['time_slot']] = $schedule;
    }
}

// Time slots for different sessions and days
$morning_slots_mon_thu = [
    '07:30-08:00' => 'Tempoh 1',
    '08:00-08:30' => 'Tempoh 2',
    '08:30-09:00' => 'Tempoh 3',
    '09:00-09:30' => 'Tempoh 4',
    '09:30-10:00' => 'Tempoh 5',
    '10:00-10:30' => 'REHAT PAGI',
    '10:30-11:00' => 'Tempoh 6',
    '11:00-11:30' => 'Tempoh 7',
    '11:30-12:00' => 'Tempoh 8',
    '12:00-12:30' => 'Tempoh 9',
    '12:30-13:00' => 'Tempoh 10',
    '13:00-13:30' => 'Tempoh 11',
    '13:30-14:00' => 'Tempoh 12',
    '14:00-14:30' => 'Tempoh 13'
];

// Special Monday morning slots (with Perhimpunan)
$morning_slots_monday = [
    '07:30-08:00' => 'PERHIMPUNAN',
    '08:00-08:30' => 'Tempoh 1',
    '08:30-09:00' => 'Tempoh 2',
    '09:00-09:30' => 'Tempoh 3',
    '09:30-10:00' => 'Tempoh 4',
    '10:00-10:30' => 'REHAT PAGI',
    '10:30-11:00' => 'Tempoh 5',
    '11:00-11:30' => 'Tempoh 6',
    '11:30-12:00' => 'Tempoh 7',
    '12:00-12:30' => 'Tempoh 8',
    '12:30-13:00' => 'Tempoh 9',
    '13:00-13:30' => 'Tempoh 10',
    '13:30-14:00' => 'Tempoh 11',
    '14:00-14:30' => 'Tempoh 12'
];

$morning_slots_friday = [
    '07:30-08:00' => 'YAASIN',
    '08:00-08:30' => 'Tempoh 1',
    '08:30-09:00' => 'Tempoh 2',
    '09:00-09:30' => 'Tempoh 3',
    '09:30-10:00' => 'Tempoh 4',
    '10:00-10:30' => 'REHAT PAGI',
    '10:30-11:00' => 'Tempoh 5',
    '11:00-11:30' => 'Tempoh 6',
    '11:30-12:00' => 'Tempoh 7'
];

$afternoon_slots_mon_thu = [
    '12:00-12:30' => 'Tempoh 1',
    '12:30-13:00' => 'Tempoh 2',
    '13:00-13:30' => 'Tempoh 3',
    '13:30-14:00' => 'Tempoh 4',
    '14:00-14:30' => 'Tempoh 5',
    '14:30-15:00' => 'Tempoh 6',
    '15:00-15:30' => 'Tempoh 7',
    '15:30-16:00' => 'Tempoh 8',
    '16:00-16:30' => 'Tempoh 9',
    '16:30-17:00' => 'Tempoh 10',
    '17:00-17:30' => 'Tempoh 11',
    '17:30-18:00' => 'Tempoh 12',
    '18:00-18:30' => 'Tempoh 13'
];

$afternoon_slots_friday = [
    '14:45-15:15' => 'Tempoh 1',
    '15:15-15:45' => 'Tempoh 2',
    '15:45-16:15' => 'Tempoh 3',
    '16:15-16:45' => 'Tempoh 4',
    '16:45-17:15' => 'Tempoh 5',
    '17:15-17:45' => 'Tempoh 6',
    '17:45-18:15' => 'Tempoh 7',
    '18:15-18:40' => 'Tempoh 8'
];

// Function to get time slots for a specific day and session
function getTimeSlots($day, $session) {
    global $morning_slots_mon_thu, $morning_slots_monday, $morning_slots_friday, $afternoon_slots_mon_thu, $afternoon_slots_friday;

    if ($session === 'morning') {
        if ($day === 'Isnin') {
            return $morning_slots_monday;
        } elseif ($day === 'Jumaat') {
            return $morning_slots_friday;
        } else {
            return $morning_slots_mon_thu;
        }
    } else { // afternoon
        if ($day === 'Jumaat') {
            return $afternoon_slots_friday;
        } else {
            return $afternoon_slots_mon_thu;
        }
    }
}

$days = ['Isnin', 'Selasa', 'Rabu', 'Khamis', 'Jumaat'];
$sessions = ['morning' => 'Sesi Pagi', 'afternoon' => 'Sesi Petang'];
?>

<!DOCTYPE html>
<html lang="ms">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pengurusan Jadual - SMKTMI</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>

<style>
.content {
    margin-left: 300px;
    padding: 40px 30px;
    min-height: 100vh;
    background-color: #f9f9f9;
}

.form-container {
    width: 100%;
    max-width: 1400px;
    background-color: #fff;
    padding: 30px 40px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0 auto;
}

.form-container h2 {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 25px;
    border-bottom: 3px solid #3498db;
    padding-bottom: 10px;
    color: #2980b9;
    text-align: center;
}

.form-section {
    display: flex;
    gap: 30px;
    align-items: flex-start;
}

.form-left {
    flex: 0 0 350px;
    max-width: 350px;
}

.form-right {
    flex: 1;
    min-width: 800px;
}

.table th {
    background-color: rgb(153, 156, 158);
    color: white;
    text-align: center;
    font-size: 14px;
    padding: 8px;
}

.table td {
    text-align: center;
    vertical-align: middle;
    padding: 5px;
}

.rehat-slot {
    background-color: #f8f9fa !important;
    color: #6c757d;
    font-style: italic;
    font-weight: bold;
}

.time-slot {
    font-weight: bold;
    background-color: #e9ecef;
    width: 120px;
}

.schedule-select {
    width: 100%;
    font-size: 12px;
    padding: 2px;
    margin: 1px 0;
}

.alert {
    padding: 15px;
    margin-bottom: 25px;
    text-align: center;
    border-radius: 5px;
}

.alert-success {
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.alert-warning {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
}

.alert-danger {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.btn-primary {
    background-color: #2980b9;
    border-color: #2980b9;
    padding: 10px 20px;
    font-weight: 600;
}

.btn-primary:hover {
    background-color: #1a5d8f;
    border-color: #1a5d8f;
}

.form-select {
    padding: 8px 12px;
    border: 1.5px solid #ccc;
    border-radius: 6px;
    margin-bottom: 15px;
}

.form-select:focus {
    border-color: #2980b9;
    box-shadow: 0 0 0 0.2rem rgba(41, 128, 185, 0.25);
}

label {
    font-weight: 600;
    color: #34495e;
    margin-bottom: 5px;
}
</style>

<?php include 'includes/sidebar.php'; ?>

<div class="content">
  <div class="form-container">
    <h2><i class="fas fa-calendar-alt"></i> Pengurusan Jadual Waktu</h2>

    <?php if (!empty($message)): ?>
      <div class="alert <?php echo $success ? 'alert-success' : ($message[0] === '⚠' ? 'alert-warning' : 'alert-danger'); ?>">
        <?php echo $message; ?>
      </div>
    <?php endif; ?>

    <?php if ($table_missing): ?>
      <div class="alert alert-danger">
        <h5><i class="fas fa-exclamation-triangle"></i> Jadual Pangkalan Data Tidak Wujud</h5>
        <p>Jadual 'schedules' tidak wujud dalam pangkalan data. Sila hubungi pentadbir sistem untuk menyediakan jadual yang diperlukan.</p>
      </div>
    <?php else: ?>

    <div class="form-section">
      <!-- Class Selection -->
      <div class="form-left">
        <h4><i class="fas fa-school"></i> Pilih Kelas</h4>
        <form method="post" action="">
          <label for="classroom_id">Kelas:</label>
          <select name="classroom_id" id="classroom_id" class="form-select" onchange="this.form.submit()">
            <option value="">-- Pilih Kelas --</option>
            <?php foreach ($classrooms as $classroom): ?>
            <option value="<?php echo $classroom['classroom_id']; ?>"
                    <?php echo ($selected_classroom_id == $classroom['classroom_id']) ? 'selected' : ''; ?>>
              <?php echo htmlspecialchars($classroom['class_name'] . ' (Tingkatan ' . $classroom['tingkatan'] . ')'); ?>
            </option>
            <?php endforeach; ?>
          </select>
        </form>

        <?php if ($selected_classroom_id && !empty($subjects)): ?>
        <div class="mt-4">
          <h5>Subjek Tersedia:</h5>
          <div class="list-group">
            <?php foreach ($subjects as $subject): ?>
            <div class="list-group-item">
              <strong><?php echo htmlspecialchars($subject['subject_name']); ?></strong><br>
              <small class="text-muted">Guru: <?php echo htmlspecialchars($subject['teacher_name']); ?></small>
            </div>
            <?php endforeach; ?>
          </div>
        </div>
        <?php endif; ?>

        <?php if ($selected_classroom_id && empty($subjects)): ?>
        <div class="alert alert-warning mt-3">
          <small><i class="fas fa-exclamation-triangle"></i> Tiada subjek ditetapkan untuk kelas ini. Sila tetapkan guru-subjek-kelas terlebih dahulu.</small>
        </div>
        <?php endif; ?>
      </div>

      <!-- Schedule Management -->
      <div class="form-right">
        <?php if ($selected_classroom_id && !empty($subjects)): ?>
        <h4><i class="fas fa-table"></i> Jadual Waktu Kelas</h4>
        <form method="post">
          <input type="hidden" name="classroom_id" value="<?php echo $selected_classroom_id; ?>">

          <?php foreach ($sessions as $session_key => $session_name): ?>
          <div class="mb-4">
            <h5 class="text-primary"><i class="fas fa-clock"></i> <?php echo $session_name; ?></h5>

            <div class="table-responsive">
              <table class="table table-bordered table-sm">
                <thead>
                  <tr>
                    <th class="time-slot">Masa</th>
                    <?php foreach ($days as $day): ?>
                    <th><?php echo $day; ?></th>
                    <?php endforeach; ?>
                  </tr>
                </thead>
                <tbody>
                  <?php
                  // Get all possible time slots for this session
                  $all_time_slots = [];
                  foreach ($days as $day) {
                      $day_slots = getTimeSlots($day, $session_key);
                      foreach ($day_slots as $time_slot => $period_name) {
                          if (!isset($all_time_slots[$time_slot])) {
                              $all_time_slots[$time_slot] = $period_name;
                          }
                      }
                  }

                  foreach ($all_time_slots as $time_slot => $period_name):
                  ?>
                  <tr>
                    <td class="time-slot <?php echo (strpos($period_name, 'REHAT') !== false) ? 'rehat-slot' : ''; ?>">
                      <strong><?php echo $period_name; ?></strong><br>
                      <small><?php echo $time_slot; ?></small>
                    </td>
                    <?php foreach ($days as $day): ?>
                    <?php
                    $day_slots = getTimeSlots($day, $session_key);
                    $day_has_slot = isset($day_slots[$time_slot]);
                    $day_period_name = $day_slots[$time_slot] ?? $period_name;
                    ?>
                    <td class="<?php echo (strpos($day_period_name, 'REHAT') !== false || strpos($day_period_name, 'PERHIMPUNAN') !== false || strpos($day_period_name, 'YAASIN') !== false) ? 'rehat-slot' : ''; ?>">
                      <?php if (!$day_has_slot): ?>
                        <em class="text-muted">-</em>
                      <?php elseif (strpos($day_period_name, 'REHAT') !== false): ?>
                        <em>Rehat</em>
                      <?php elseif (strpos($day_period_name, 'PERHIMPUNAN') !== false): ?>
                        <em><strong>Perhimpunan</strong></em>
                      <?php elseif (strpos($day_period_name, 'YAASIN') !== false): ?>
                        <em><strong>Yaasin</strong></em>
                      <?php else: ?>
                        <?php
                        $current_schedule = $existing_schedules[$session_key][$day][$time_slot] ?? null;
                        ?>

                        <!-- Teacher Selection -->
                        <select name="schedules[<?php echo $session_key; ?>][<?php echo $day; ?>][<?php echo $time_slot; ?>][teacher_id]" class="schedule-select">
                          <option value="">-- Pilih Guru --</option>
                          <?php foreach ($subjects as $subject): ?>
                          <option value="<?php echo $subject['teacher_id']; ?>"
                                  data-subject="<?php echo $subject['subject_id']; ?>"
                                  <?php echo ($current_schedule && $current_schedule['teacher_id'] == $subject['teacher_id']) ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($subject['teacher_name']); ?>
                          </option>
                          <?php endforeach; ?>
                        </select>

                        <!-- Subject Selection -->
                        <select name="schedules[<?php echo $session_key; ?>][<?php echo $day; ?>][<?php echo $time_slot; ?>][subject_id]" class="schedule-select">
                          <option value="">-- Pilih Subjek --</option>
                          <?php foreach ($subjects as $subject): ?>
                          <option value="<?php echo $subject['subject_id']; ?>"
                                  <?php echo ($current_schedule && $current_schedule['subject_id'] == $subject['subject_id']) ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($subject['subject_name']); ?>
                          </option>
                          <?php endforeach; ?>
                        </select>
                      <?php endif; ?>
                    </td>
                    <?php endforeach; ?>
                  </tr>
                  <?php endforeach; ?>
                </tbody>
              </table>
            </div>
          </div>
          <?php endforeach; ?>

          <div class="text-center mt-3">
            <button type="submit" name="manage_class_schedule" class="btn btn-primary btn-lg">
              <i class="fas fa-save"></i> Simpan Jadual Kelas
            </button>
          </div>
        </form>

        <?php else: ?>
        <div class="text-center text-muted mt-5">
          <i class="fas fa-calendar-times fa-3x mb-3"></i>
          <h5>Pilih Kelas untuk Mengurus Jadual</h5>
          <p>Sila pilih kelas dari senarai di sebelah kiri untuk mula mengurus jadual waktu.</p>
        </div>
        <?php endif; ?>
      </div>
    </div>
  </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script>
// Auto-sync teacher and subject selections
document.addEventListener('DOMContentLoaded', function() {
    const teacherSelects = document.querySelectorAll('select[name*="[teacher_id]"]');
    const subjectSelects = document.querySelectorAll('select[name*="[subject_id]"]');

    // Create mapping of teacher to subjects
    const teacherSubjects = {};
    <?php foreach ($subjects as $subject): ?>
    teacherSubjects[<?php echo $subject['teacher_id']; ?>] = <?php echo $subject['subject_id']; ?>;
    <?php endforeach; ?>

    // When teacher is selected, auto-select corresponding subject
    teacherSelects.forEach(function(teacherSelect) {
        teacherSelect.addEventListener('change', function() {
            const teacherId = this.value;
            const timeSlot = this.name.match(/\[([^\]]+)\]\[([^\]]+)\]\[([^\]]+)\]/);
            if (timeSlot) {
                const session = timeSlot[1];
                const day = timeSlot[2];
                const time = timeSlot[3];
                const subjectSelect = document.querySelector(`select[name="schedules[${session}][${day}][${time}][subject_id]"]`);

                if (teacherId && teacherSubjects[teacherId] && subjectSelect) {
                    subjectSelect.value = teacherSubjects[teacherId];
                } else if (subjectSelect) {
                    subjectSelect.value = '';
                }
            }
        });
    });

    // When subject is selected, auto-select corresponding teacher
    subjectSelects.forEach(function(subjectSelect) {
        subjectSelect.addEventListener('change', function() {
            const subjectId = this.value;
            const timeSlot = this.name.match(/\[([^\]]+)\]\[([^\]]+)\]\[([^\]]+)\]/);
            if (timeSlot) {
                const session = timeSlot[1];
                const day = timeSlot[2];
                const time = timeSlot[3];
                const teacherSelect = document.querySelector(`select[name="schedules[${session}][${day}][${time}][teacher_id]"]`);

                if (subjectId && teacherSelect) {
                    // Find teacher for this subject
                    for (let teacherId in teacherSubjects) {
                        if (teacherSubjects[teacherId] == subjectId) {
                            teacherSelect.value = teacherId;
                            break;
                        }
                    }
                } else if (teacherSelect) {
                    teacherSelect.value = '';
                }
            }
        });
    });
});
</script>

<?php endif; // End table_missing check ?>

</body>
</html>
