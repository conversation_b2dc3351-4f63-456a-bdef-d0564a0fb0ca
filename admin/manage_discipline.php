<?php
session_start();

if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'Admin') {
    header("Location: ../login.php");
    exit;
}

require '../db.php';

// Check if discipline_incidents table exists and add missing columns if needed
try {
    // Check if table exists
    $tableCheck = $conn->query("SHOW TABLES LIKE 'discipline_incidents'");
    if ($tableCheck && $tableCheck->num_rows > 0) {
        // Table exists, check for missing columns
        $columns = $conn->query("SHOW COLUMNS FROM discipline_incidents");
        $existing_columns = [];
        while ($col = $columns->fetch_assoc()) {
            $existing_columns[] = $col['Field'];
        }

        // Define required columns with their definitions
        $required_columns = [
            'teacher_id' => 'int(11) NOT NULL',
            'action_taken' => 'text',
            'severity' => "enum('Ringan','Sederhana','Berat') DEFAULT 'Ringan'",
            'status' => "enum('Baru','<PERSON><PERSON>','Selesai') DEFAULT 'Baru'",
            'updated_at' => 'timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
        ];

        // Add missing columns
        foreach ($required_columns as $column => $definition) {
            if (!in_array($column, $existing_columns)) {
                $alterSQL = "ALTER TABLE discipline_incidents ADD COLUMN $column $definition";
                $conn->query($alterSQL);
            }
        }
    }
} catch (Exception $e) {
    // Continue if table doesn't exist or column addition fails
}

$message = "";
$success = false;

// Handle table verification request
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['verify_table'])) {
    try {
        // Check if the table exists
        $tableCheck = $conn->query("SHOW TABLES LIKE 'discipline_incidents'");

        if ($tableCheck->num_rows == 0) {
            // Table doesn't exist
            $message = "❌ Jadual discipline_incidents tidak wujud. Sila hubungi pentadbir sistem untuk menyediakan jadual pangkalan data.";
            $success = false;
        } else {
            // Table exists, check for missing columns
            $columns = $conn->query("SHOW COLUMNS FROM discipline_incidents");
            $existing_columns = [];
            while ($col = $columns->fetch_assoc()) {
                $existing_columns[] = $col['Field'];
            }

            $required_columns = [
                'teacher_id' => 'int(11) NOT NULL',
                'action_taken' => 'text',
                'severity' => "enum('Ringan','Sederhana','Berat') DEFAULT 'Ringan'",
                'status' => "enum('Baru','Dalam Tindakan','Selesai') DEFAULT 'Baru'",
                'updated_at' => 'timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
            ];

            $added_columns = [];
            foreach ($required_columns as $column => $definition) {
                if (!in_array($column, $existing_columns)) {
                    $alterSQL = "ALTER TABLE discipline_incidents ADD COLUMN $column $definition";
                    $conn->query($alterSQL);
                    $added_columns[] = $column;
                }
            }

            if (!empty($added_columns)) {
                $message = "✅ Kolom yang ditambah: " . implode(', ', $added_columns);
                $success = true;
            } else {
                $message = "ℹ️ Jadual discipline_incidents sudah lengkap dan sedia digunakan.";
                $success = true;
            }
        }

    } catch (Exception $e) {
        $message = "❌ Ralat: " . $e->getMessage();
    }
}

// Handle form submissions - Admin can only update existing records
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Removed add_incident functionality - only teachers can create incidents
    
    if (isset($_POST['update_status'])) {
        $incident_id = (int)$_POST['incident_id'];
        $status = $_POST['status'];
        $action_taken = trim($_POST['action_taken']);

        if ($incident_id <= 0) {
            $message = "❌ ID laporan tidak sah.";
        } elseif (empty($status)) {
            $message = "❌ Sila pilih status.";
        } else {
            try {
                $stmt = $conn->prepare("UPDATE discipline_incidents SET status = ?, action_taken = ?, updated_at = CURRENT_TIMESTAMP WHERE incident_id = ?");
                $stmt->bind_param("ssi", $status, $action_taken, $incident_id);

                if ($stmt->execute()) {
                    if ($stmt->affected_rows > 0) {
                        $message = "✅ Status laporan berjaya dikemaskini.";
                        $success = true;
                    } else {
                        $message = "❌ Laporan tidak dijumpai atau tiada perubahan.";
                    }
                } else {
                    $message = "❌ Ralat: " . $stmt->error;
                }
            } catch (Exception $e) {
                $message = "❌ Ralat sistem: " . $e->getMessage();
            }
        }
    }
}

// Get filter parameters
$filter_class = isset($_GET['class']) ? (int)$_GET['class'] : 0;
$filter_severity = isset($_GET['severity']) ? $_GET['severity'] : '';
$filter_status = isset($_GET['status']) ? $_GET['status'] : '';

// Get all incidents with filters
$whereConditions = [];
$params = [];
$types = "";

if ($filter_class > 0) {
    $whereConditions[] = "s.classroom_id = ?";
    $params[] = $filter_class;
    $types .= "i";
}

if ($filter_severity) {
    $whereConditions[] = "di.severity = ?";
    $params[] = $filter_severity;
    $types .= "s";
}

if ($filter_status) {
    $whereConditions[] = "di.status = ?";
    $params[] = $filter_status;
    $types .= "s";
}

$whereClause = !empty($whereConditions) ? "WHERE " . implode(" AND ", $whereConditions) : "";

// Get incidents with error handling
$incidents = null;
try {
    $incidentsSQL = "
        SELECT di.*, s.full_name as student_name, s.no_ic, c.class_name, t.full_name as teacher_name
        FROM discipline_incidents di
        JOIN students s ON di.student_id = s.student_id
        LEFT JOIN classrooms c ON s.classroom_id = c.classroom_id
        JOIN teachers t ON di.teacher_id = t.teacher_id
        $whereClause
        ORDER BY di.incident_date DESC, di.created_at DESC
    ";

    $stmt = $conn->prepare($incidentsSQL);
    if (!empty($params)) {
        $stmt->bind_param($types, ...$params);
    }
    $stmt->execute();
    $incidents = $stmt->get_result();
} catch (Exception $e) {
    // Handle case where table doesn't exist or query fails
    $incidents = null;
    if (!$message) {
        $message = "⚠️ Sistem disiplin belum diaktifkan sepenuhnya.";
    }
}

// Get students and teachers for dropdowns with error handling
try {
    $students = $conn->query("SELECT s.student_id, s.full_name, c.class_name FROM students s LEFT JOIN classrooms c ON s.classroom_id = c.classroom_id ORDER BY s.full_name");
    $teachers = $conn->query("SELECT teacher_id, full_name FROM teachers ORDER BY full_name");
    $classrooms = $conn->query("SELECT classroom_id, class_name FROM classrooms ORDER BY class_name");
} catch (Exception $e) {
    // Handle database errors
    $students = null;
    $teachers = null;
    $classrooms = null;
    if (!$message) {
        $message = "⚠️ Ralat mengambil data: " . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="ms">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pengurusan Disiplin - SMKTMI</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>

<style>
/* Content Styling */
.content {
    margin-left: 300px;
    padding: 40px 30px;
    min-height: 100vh;
    background-color: #f9f9f9;
}

.form-container {
    width: 100%;
    max-width: 1400px;
    background-color: #fff;
    padding: 30px 40px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0 auto;
}

.form-container h2 {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 25px;
    border-bottom: 3px solid #3498db;
    padding-bottom: 10px;
    color: #2980b9;
    text-align: center;
}

.filter-section {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 30px;
    border-left: 4px solid #3498db;
}

.incident-table th {
    background-color: rgb(153, 156, 158);
    color: white;
    text-align: center;
    font-weight: 600;
    padding: 12px;
    border: none;
}

.incident-table td {
    padding: 12px;
    text-align: center;
    vertical-align: middle;
    border-bottom: 1px solid #e9ecef;
}

.severity-ringan { background-color: #d4edda; color: #155724; }
.severity-sederhana { background-color: #fff3cd; color: #856404; }
.severity-berat { background-color: #f8d7da; color: #721c24; }

.status-baru { background-color: #cce5ff; color: #004085; }
.status-dalam-tindakan { background-color: #fff3cd; color: #856404; }
.status-selesai { background-color: #d4edda; color: #155724; }

.badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
}

/* Responsive */
@media (max-width: 768px) {
    .content {
        margin-left: 0;
        padding: 20px;
    }
    
    .form-container {
        padding: 20px;
    }
}
</style>

<?php include 'includes/sidebar.php'; ?>

<div class="content">
  <div class="form-container">
    <h2><i class="fas fa-cogs"></i> Pengurusan Rekod Disiplin</h2>

    <div class="alert alert-primary">
      <i class="fas fa-info-circle"></i> <strong>Peranan Admin:</strong> Anda boleh melihat dan menguruskan semua rekod disiplin yang dibuat oleh guru. Hanya guru yang boleh membuat laporan disiplin baru.
    </div>

    <?php if (!empty($message)): ?>
      <div class="alert <?php echo $success ? 'alert-success' : 'alert-danger'; ?>">
        <?php echo $message; ?>
      </div>
    <?php endif; ?>



    <!-- Filter Section -->
    <div class="filter-section">
      <h5 style="margin-bottom: 15px; color: #2c3e50;"><i class="fas fa-filter"></i> Penapis</h5>
      <form method="get" class="row g-3">
        <div class="col-md-4">
          <label for="class" class="form-label">Kelas:</label>
          <select name="class" id="class" class="form-select">
            <option value="">-- Semua Kelas --</option>
            <?php if ($classrooms && $classrooms->num_rows > 0): ?>
              <?php
              $classrooms->data_seek(0); // Reset pointer
              while ($classroom = $classrooms->fetch_assoc()): ?>
                <option value="<?php echo $classroom['classroom_id']; ?>"
                        <?php echo ($filter_class == $classroom['classroom_id']) ? 'selected' : ''; ?>>
                  <?php echo htmlspecialchars($classroom['class_name']); ?>
                </option>
              <?php endwhile; ?>
            <?php endif; ?>
          </select>
        </div>
        <div class="col-md-4">
          <label for="severity" class="form-label">Tahap Keseriusan:</label>
          <select name="severity" id="severity" class="form-select">
            <option value="">-- Semua Tahap --</option>
            <option value="Ringan" <?php echo ($filter_severity == 'Ringan') ? 'selected' : ''; ?>>Ringan</option>
            <option value="Sederhana" <?php echo ($filter_severity == 'Sederhana') ? 'selected' : ''; ?>>Sederhana</option>
            <option value="Berat" <?php echo ($filter_severity == 'Berat') ? 'selected' : ''; ?>>Berat</option>
          </select>
        </div>
        <div class="col-md-4">
          <label for="status" class="form-label">Status:</label>
          <select name="status" id="status" class="form-select">
            <option value="">-- Semua Status --</option>
            <option value="Baru" <?php echo ($filter_status == 'Baru') ? 'selected' : ''; ?>>Baru</option>
            <option value="Dalam Tindakan" <?php echo ($filter_status == 'Dalam Tindakan') ? 'selected' : ''; ?>>Dalam Tindakan</option>
            <option value="Selesai" <?php echo ($filter_status == 'Selesai') ? 'selected' : ''; ?>>Selesai</option>
          </select>
        </div>
        <div class="col-12">
          <button type="submit" class="btn btn-primary">
            <i class="fas fa-search"></i> Tapis
          </button>
          <a href="manage_discipline.php" class="btn btn-secondary">
            <i class="fas fa-refresh"></i> Reset
          </a>
          <div class="alert alert-info mt-3 mb-0">
            <i class="fas fa-info-circle"></i> <strong>Nota:</strong> Hanya guru boleh membuat laporan disiplin baru. Admin boleh menguruskan status dan tindakan susulan sahaja.
          </div>
        </div>
      </form>
    </div>

    <!-- Incidents Table -->
    <div class="table-responsive">
      <table class="table table-bordered incident-table">
        <thead>
          <tr>
            <th>Bil.</th>
            <th>Tarikh</th>
            <th>Pelajar</th>
            <th>Kelas</th>
            <th>Jenis Kesalahan</th>
            <th>Tahap</th>
            <th>Status</th>
            <th>Guru Pelapor</th>
            <th>Tindakan</th>
          </tr>
        </thead>
        <tbody>
          <?php if ($incidents && $incidents->num_rows > 0): ?>
            <?php $bil = 1; while ($incident = $incidents->fetch_assoc()): ?>
              <tr>
                <td><?php echo $bil++; ?></td>
                <td><?php echo date('d/m/Y', strtotime($incident['incident_date'])); ?></td>
                <td><?php echo htmlspecialchars($incident['student_name']); ?></td>
                <td><?php echo htmlspecialchars($incident['class_name'] ?? '-'); ?></td>
                <td><?php echo htmlspecialchars($incident['incident_type']); ?></td>
                <td>
                  <span class="badge severity-<?php echo strtolower($incident['severity']); ?>">
                    <?php echo $incident['severity']; ?>
                  </span>
                </td>
                <td>
                  <span class="badge status-<?php echo strtolower(str_replace(' ', '-', $incident['status'])); ?>">
                    <?php echo $incident['status']; ?>
                  </span>
                </td>
                <td><?php echo htmlspecialchars($incident['teacher_name']); ?></td>
                <td>
                  <button class="btn btn-sm btn-primary" onclick="viewIncident(<?php echo $incident['incident_id']; ?>, '<?php echo addslashes($incident['description']); ?>', '<?php echo addslashes($incident['action_taken']); ?>')" title="Lihat Butiran">
                    <i class="fas fa-eye"></i>
                  </button>
                  <button class="btn btn-sm btn-warning" onclick="editIncident(<?php echo $incident['incident_id']; ?>, '<?php echo $incident['status']; ?>', '<?php echo addslashes($incident['action_taken']); ?>')" title="Kemaskini Status">
                    <i class="fas fa-edit"></i>
                  </button>
                </td>
              </tr>
            <?php endwhile; ?>
          <?php else: ?>
            <tr>
              <td colspan="9" class="text-center">
                <?php if ($incidents === null): ?>
                  Sistem disiplin belum diaktifkan atau terdapat masalah dengan pangkalan data.
                <?php else: ?>
                  Tiada laporan disiplin dijumpai.
                <?php endif; ?>
              </td>
            </tr>
          <?php endif; ?>
        </tbody>
      </table>
    </div>
  </div>
</div>

<!-- Admin can only view and manage existing records, not create new ones -->

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script>
function viewIncident(incidentId, description, actionTaken) {
    let content = `
        <div class="mb-3">
            <strong>ID Laporan:</strong> ${incidentId}
        </div>
        <div class="mb-3">
            <strong>Penerangan Kejadian:</strong><br>
            ${description || 'Tiada penerangan'}
        </div>
        <div class="mb-3">
            <strong>Tindakan Diambil:</strong><br>
            ${actionTaken || 'Tiada tindakan dicatat'}
        </div>
    `;

    document.getElementById('viewModalBody').innerHTML = content;
    new bootstrap.Modal(document.getElementById('viewIncidentModal')).show();
}

function editIncident(incidentId, currentStatus, currentAction) {
    document.getElementById('edit_incident_id').value = incidentId;
    document.getElementById('edit_status').value = currentStatus;
    document.getElementById('edit_action_taken').value = currentAction || '';

    new bootstrap.Modal(document.getElementById('editIncidentModal')).show();
}
</script>

<!-- View Incident Modal -->
<div class="modal fade" id="viewIncidentModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Butiran Laporan Disiplin</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body" id="viewModalBody">
        <!-- Content will be populated by JavaScript -->
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
      </div>
    </div>
  </div>
</div>

<!-- Edit Incident Modal -->
<div class="modal fade" id="editIncidentModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Kemaskini Status Laporan</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <form method="post">
        <div class="modal-body">
          <input type="hidden" name="incident_id" id="edit_incident_id">
          <div class="mb-3">
            <label for="edit_status" class="form-label">Status:</label>
            <select name="status" id="edit_status" class="form-select" required>
              <option value="Baru">Baru</option>
              <option value="Dalam Tindakan">Dalam Tindakan</option>
              <option value="Selesai">Selesai</option>
            </select>
          </div>
          <div class="mb-3">
            <label for="edit_action_taken" class="form-label">Tindakan Diambil:</label>
            <textarea name="action_taken" id="edit_action_taken" class="form-control" rows="4"
                      placeholder="Nyatakan tindakan yang telah diambil..."></textarea>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
          <button type="submit" name="update_status" class="btn btn-primary">Kemaskini</button>
        </div>
      </form>
    </div>
  </div>
</div>
</body>
</html>

