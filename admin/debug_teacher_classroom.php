<?php
session_start();

if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'Admin') {
    header("Location: ../login.php");
    exit;
}

require '../db.php';

echo "<h2>🔍 Teacher-Classroom Assignment Debug Tool</h2>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    table { border-collapse: collapse; width: 100%; margin: 20px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    .error { color: red; }
    .success { color: green; }
    .warning { color: orange; }
    .section { margin: 30px 0; padding: 20px; border: 1px solid #ccc; border-radius: 5px; }
</style>";

// 1. Check all users with Teacher role
echo "<div class='section'>";
echo "<h3>1. All Teacher Users</h3>";
try {
    $result = $conn->query("
        SELECT u.user_id, u.email, u.role_id, t.teacher_id, t.full_name, t.staff_id
        FROM users u
        LEFT JOIN teachers t ON u.user_id = t.user_id
        WHERE u.role_id = 3
        ORDER BY u.user_id
    ");
    
    if ($result && $result->num_rows > 0) {
        echo "<table>";
        echo "<tr><th>User ID</th><th>Email</th><th>Teacher ID</th><th>Full Name</th><th>Staff ID</th><th>Status</th></tr>";
        while ($row = $result->fetch_assoc()) {
            $status = $row['teacher_id'] ? "<span class='success'>✅ Linked</span>" : "<span class='error'>❌ No teacher record</span>";
            echo "<tr>";
            echo "<td>{$row['user_id']}</td>";
            echo "<td>{$row['email']}</td>";
            echo "<td>" . ($row['teacher_id'] ?: 'NULL') . "</td>";
            echo "<td>" . ($row['full_name'] ?: 'NULL') . "</td>";
            echo "<td>" . ($row['staff_id'] ?: 'NULL') . "</td>";
            echo "<td>$status</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p class='error'>No teacher users found!</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>Error: " . $e->getMessage() . "</p>";
}
echo "</div>";

// 2. Check all classrooms and their assigned teachers
echo "<div class='section'>";
echo "<h3>2. All Classrooms and Teacher Assignments</h3>";
try {
    $result = $conn->query("
        SELECT c.classroom_id, c.class_name, c.teacher_id, t.full_name as teacher_name, t.staff_id, u.email
        FROM classrooms c
        LEFT JOIN teachers t ON c.teacher_id = t.teacher_id
        LEFT JOIN users u ON t.user_id = u.user_id
        ORDER BY c.class_name
    ");
    
    if ($result && $result->num_rows > 0) {
        echo "<table>";
        echo "<tr><th>Classroom ID</th><th>Class Name</th><th>Teacher ID</th><th>Teacher Name</th><th>Staff ID</th><th>Email</th><th>Status</th></tr>";
        while ($row = $result->fetch_assoc()) {
            $status = $row['teacher_id'] ? "<span class='success'>✅ Assigned</span>" : "<span class='warning'>⚠️ No teacher</span>";
            echo "<tr>";
            echo "<td>{$row['classroom_id']}</td>";
            echo "<td><strong>{$row['class_name']}</strong></td>";
            echo "<td>" . ($row['teacher_id'] ?: 'NULL') . "</td>";
            echo "<td>" . ($row['teacher_name'] ?: 'NULL') . "</td>";
            echo "<td>" . ($row['staff_id'] ?: 'NULL') . "</td>";
            echo "<td>" . ($row['email'] ?: 'NULL') . "</td>";
            echo "<td>$status</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p class='error'>No classrooms found!</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>Error: " . $e->getMessage() . "</p>";
}
echo "</div>";

// 3. Check for specific issues
echo "<div class='section'>";
echo "<h3>3. Potential Issues</h3>";

// Check for teachers without classrooms
try {
    $result = $conn->query("
        SELECT t.teacher_id, t.full_name, t.staff_id, u.email
        FROM teachers t
        JOIN users u ON t.user_id = u.user_id
        WHERE t.teacher_id NOT IN (SELECT teacher_id FROM classrooms WHERE teacher_id IS NOT NULL)
        ORDER BY t.full_name
    ");
    
    if ($result && $result->num_rows > 0) {
        echo "<h4 class='warning'>⚠️ Teachers without assigned classrooms:</h4>";
        echo "<table>";
        echo "<tr><th>Teacher ID</th><th>Full Name</th><th>Staff ID</th><th>Email</th></tr>";
        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>{$row['teacher_id']}</td>";
            echo "<td>{$row['full_name']}</td>";
            echo "<td>{$row['staff_id']}</td>";
            echo "<td>{$row['email']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p class='success'>✅ All teachers have assigned classrooms</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>Error checking unassigned teachers: " . $e->getMessage() . "</p>";
}

// Check for classrooms without teachers
try {
    $result = $conn->query("
        SELECT classroom_id, class_name
        FROM classrooms
        WHERE teacher_id IS NULL
        ORDER BY class_name
    ");
    
    if ($result && $result->num_rows > 0) {
        echo "<h4 class='warning'>⚠️ Classrooms without assigned teachers:</h4>";
        echo "<table>";
        echo "<tr><th>Classroom ID</th><th>Class Name</th></tr>";
        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>{$row['classroom_id']}</td>";
            echo "<td><strong>{$row['class_name']}</strong></td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p class='success'>✅ All classrooms have assigned teachers</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>Error checking unassigned classrooms: " . $e->getMessage() . "</p>";
}
echo "</div>";

// 4. Check for specific "5 Hambali" and "5 Ghazali" classes
echo "<div class='section'>";
echo "<h3>4. Specific Class Check: 5 Hambali vs 5 Ghazali</h3>";
try {
    $result = $conn->query("
        SELECT c.classroom_id, c.class_name, c.teacher_id, t.full_name as teacher_name, t.staff_id, u.email, u.user_id
        FROM classrooms c
        LEFT JOIN teachers t ON c.teacher_id = t.teacher_id
        LEFT JOIN users u ON t.user_id = u.user_id
        WHERE c.class_name LIKE '%Hambali%' OR c.class_name LIKE '%Ghazali%'
        ORDER BY c.class_name
    ");
    
    if ($result && $result->num_rows > 0) {
        echo "<table>";
        echo "<tr><th>Classroom ID</th><th>Class Name</th><th>Teacher ID</th><th>User ID</th><th>Teacher Name</th><th>Email</th></tr>";
        while ($row = $result->fetch_assoc()) {
            $highlight = (strpos($row['class_name'], 'Hambali') !== false) ? "style='background-color: #ffffcc;'" : "";
            echo "<tr $highlight>";
            echo "<td>{$row['classroom_id']}</td>";
            echo "<td><strong>{$row['class_name']}</strong></td>";
            echo "<td>" . ($row['teacher_id'] ?: 'NULL') . "</td>";
            echo "<td>" . ($row['user_id'] ?: 'NULL') . "</td>";
            echo "<td>" . ($row['teacher_name'] ?: 'NULL') . "</td>";
            echo "<td>" . ($row['email'] ?: 'NULL') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p class='warning'>No classes found with 'Hambali' or 'Ghazali' in the name</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>Error: " . $e->getMessage() . "</p>";
}
echo "</div>";

// 5. Students in these classes
echo "<div class='section'>";
echo "<h3>5. Students in Hambali/Ghazali Classes</h3>";
try {
    $result = $conn->query("
        SELECT c.class_name, COUNT(s.student_id) as student_count
        FROM classrooms c
        LEFT JOIN students s ON c.classroom_id = s.classroom_id
        WHERE c.class_name LIKE '%Hambali%' OR c.class_name LIKE '%Ghazali%'
        GROUP BY c.classroom_id, c.class_name
        ORDER BY c.class_name
    ");
    
    if ($result && $result->num_rows > 0) {
        echo "<table>";
        echo "<tr><th>Class Name</th><th>Student Count</th></tr>";
        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td><strong>{$row['class_name']}</strong></td>";
            echo "<td>{$row['student_count']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} catch (Exception $e) {
    echo "<p class='error'>Error: " . $e->getMessage() . "</p>";
}
echo "</div>";

echo "<div class='section'>";
echo "<h3>6. Quick Fix Actions</h3>";
echo "<p>If you need to fix teacher-classroom assignments, you can:</p>";
echo "<ol>";
echo "<li><strong>Assign teacher to classroom:</strong> <code>UPDATE classrooms SET teacher_id = [TEACHER_ID] WHERE classroom_id = [CLASSROOM_ID];</code></li>";
echo "<li><strong>Remove assignment:</strong> <code>UPDATE classrooms SET teacher_id = NULL WHERE classroom_id = [CLASSROOM_ID];</code></li>";
echo "<li><strong>Check teacher login:</strong> Use the User ID from section 1 to see which teacher account is being used</li>";
echo "</ol>";
echo "</div>";

echo "<p><a href='list_students.php'>← Back to Admin Student List</a> | <a href='add_classroom.php'>Manage Classrooms</a> | <a href='assign_teacher.php'>Assign Teachers</a></p>";
?>
