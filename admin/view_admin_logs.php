<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'Admin') {
    header("Location: ../login.php");
    exit;
}

require '../db.php';
require '../includes/universal_logger.php';

// Pagination settings
$records_per_page = 20;
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$offset = ($page - 1) * $records_per_page;

// Filter settings
$action_filter = isset($_GET['action']) ? $_GET['action'] : '';
$target_filter = isset($_GET['target']) ? $_GET['target'] : '';
$date_filter = isset($_GET['date']) ? $_GET['date'] : '';

// Build WHERE clause
$where_conditions = [];
$params = [];
$param_types = '';

if (!empty($action_filter)) {
    $where_conditions[] = "action = ?";
    $params[] = $action_filter;
    $param_types .= 's';
}

if (!empty($target_filter)) {
    $where_conditions[] = "target_type = ?";
    $params[] = $target_filter;
    $param_types .= 's';
}

if (!empty($date_filter)) {
    $where_conditions[] = "DATE(created_at) = ?";
    $params[] = $date_filter;
    $param_types .= 's';
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Get total records for pagination
$count_sql = "SELECT COUNT(*) as total FROM admin_logs $where_clause";
if (!empty($params)) {
    $count_stmt = $conn->prepare($count_sql);
    $count_stmt->bind_param($param_types, ...$params);
    $count_stmt->execute();
    $count_result = $count_stmt->get_result();
    $total_records = $count_result->fetch_assoc()['total'];
    $count_stmt->close();
} else {
    $count_result = $conn->query($count_sql);
    $total_records = $count_result->fetch_assoc()['total'];
}

$total_pages = ceil($total_records / $records_per_page);

// Get logs with pagination
$sql = "SELECT * FROM admin_logs $where_clause ORDER BY created_at DESC LIMIT ? OFFSET ?";
$params[] = $records_per_page;
$params[] = $offset;
$param_types .= 'ii';

$stmt = $conn->prepare($sql);
if (!empty($where_conditions)) {
    $stmt->bind_param($param_types, ...$params);
} else {
    $stmt->bind_param('ii', $records_per_page, $offset);
}
$stmt->execute();
$logs_result = $stmt->get_result();

// Get unique actions and target types for filters
$actions_result = $conn->query("SELECT DISTINCT action FROM admin_logs ORDER BY action");
$targets_result = $conn->query("SELECT DISTINCT target_type FROM admin_logs ORDER BY target_type");

include 'includes/header.php';
?>

<!-- FontAwesome CDN for icons -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

<style>
/* Content Styling */
.content {
    margin-left: 280px;
    padding: 40px 30px;
    min-height: 100vh;
    background-color: #f9f9f9;
}

.logs-container {
    width: 100%;
    background-color: #fff;
    padding: 30px 40px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.logs-container h2 {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 25px;
    border-bottom: 3px solid #3498db;
    padding-bottom: 10px;
    color: #2980b9;
}

.filters {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 6px;
    margin-bottom: 25px;
    border: 1px solid #e9ecef;
}

.filter-row {
    display: flex;
    gap: 15px;
    align-items: end;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    min-width: 150px;
}

.filter-group label {
    font-weight: 600;
    margin-bottom: 5px;
    color: #495057;
}

.filter-group select,
.filter-group input {
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
}

.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    text-decoration: none;
    font-size: 14px;
    height: fit-content;
}

.btn-primary {
    background-color: #3498db;
    color: white;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.logs-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

.logs-table th,
.logs-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #dee2e6;
    vertical-align: top;
}

.logs-table th:nth-child(6),
.logs-table td:nth-child(6) {
    width: 40%;
    max-width: 400px;
    word-wrap: break-word;
    white-space: normal;
    line-height: 1.5;
    padding: 15px 12px;
}

.logs-table th {
    background-color: rgb(153, 156, 158);
    color: white;
    font-weight: 600;
    position: sticky;
    top: 0;
}

.logs-table tr:hover {
    background-color: #f8f9fa;
}

.action-badge {
    padding: 4px 8px;
    border-radius: 12px;
    color: white;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.target-icon {
    margin-right: 5px;
}

.badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    display: inline-block;
}

.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 30px;
    gap: 10px;
}

.pagination a,
.pagination span {
    padding: 8px 12px;
    border: 1px solid #dee2e6;
    text-decoration: none;
    color: #495057;
    border-radius: 4px;
}

.pagination a:hover {
    background-color: #e9ecef;
}

.pagination .current {
    background-color: #3498db;
    color: white;
    border-color: #3498db;
}

.stats {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.stat-card {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
    border-left: 4px solid #3498db;
    flex: 1;
}

.stat-number {
    font-size: 24px;
    font-weight: 700;
    color: #2980b9;
}

.stat-label {
    font-size: 14px;
    color: #6c757d;
}
</style>

<?php include 'includes/sidebar.php'; ?>

<div class="content">
    <div class="logs-container">
        <h2><i class="fas fa-history"></i> Log Aktiviti Sistem</h2>
        
        <!-- Statistics -->
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number"><?php echo number_format($total_records); ?></div>
                <div class="stat-label">Jumlah Log</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $total_pages; ?></div>
                <div class="stat-label">Halaman</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $page; ?></div>
                <div class="stat-label">Halaman Semasa</div>
            </div>
        </div>
        
        <!-- Filters -->
        <div class="filters">
            <form method="GET" action="">
                <div class="filter-row">
                    <div class="filter-group">
                        <label>Tindakan:</label>
                        <select name="action">
                            <option value="">Semua Tindakan</option>
                            <?php while ($action = $actions_result->fetch_assoc()): ?>
                                <option value="<?php echo htmlspecialchars($action['action']); ?>" 
                                        <?php echo $action_filter === $action['action'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($action['action']); ?>
                                </option>
                            <?php endwhile; ?>
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <label>Jenis Target:</label>
                        <select name="target">
                            <option value="">Semua Jenis</option>
                            <?php while ($target = $targets_result->fetch_assoc()): ?>
                                <option value="<?php echo htmlspecialchars($target['target_type']); ?>" 
                                        <?php echo $target_filter === $target['target_type'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($target['target_type']); ?>
                                </option>
                            <?php endwhile; ?>
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <label>Tarikh:</label>
                        <input type="date" name="date" value="<?php echo htmlspecialchars($date_filter); ?>">
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter"></i> Tapis
                    </button>
                    
                    <a href="view_admin_logs.php" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Reset
                    </a>
                </div>
            </form>
        </div>

        <!-- Logs Table -->
        <?php if ($logs_result->num_rows > 0): ?>
            <table class="logs-table">
                <thead>
                    <tr>
                        <th>Tarikh & Masa</th>
                        <th>Pengguna</th>
                        <th>Peranan</th>
                        <th>Tindakan</th>
                        <th>Jenis Target</th>
                        <th>Keterangan</th>
                    </tr>
                </thead>
                <tbody>
                    <?php while ($log = $logs_result->fetch_assoc()): ?>
                        <?php
                        // Extract role from description if it exists
                        $role = 'Admin'; // Default
                        $clean_description = $log['description'] ?? '';
                        if (!empty($log['description']) && preg_match('/^\[(\w+)\]\s*(.*)/', $log['description'], $matches)) {
                            $role = $matches[1];
                            $clean_description = $matches[2];
                        }

                        // If description is still empty, provide a default
                        if (empty($clean_description)) {
                            $clean_description = 'No description available';
                        }

                        // If admin_name is empty, try to get it from user_id
                        $display_name = $log['admin_name'];
                        if (empty($display_name) || $display_name === 'Unknown User') {
                            $display_name = 'User ID: ' . $log['admin_id'];
                        }

                        // Debug: Check if functions exist
                        if (!function_exists('getUserRoleColor')) {
                            $role_color = '#95a5a6'; // Default gray
                        } else {
                            $role_color = getUserRoleColor($role);
                        }

                        if (!function_exists('getActionColor')) {
                            $action_color = '#34495e'; // Default dark gray
                        } else {
                            $action_color = getActionColor($log['action']);
                        }

                        if (!function_exists('getTargetTypeIcon')) {
                            $target_icon = 'fas fa-file'; // Default icon
                        } else {
                            $target_icon = getTargetTypeIcon($log['target_type']);
                        }
                        ?>
                        <tr>
                            <td><?php echo date('d/m/Y H:i:s', strtotime($log['created_at'])); ?></td>
                            <td><?php echo htmlspecialchars($display_name); ?></td>
                            <td>
                                <span class="badge" style="background-color: <?php echo $role_color; ?>; color: white;">
                                    <?php echo htmlspecialchars($role); ?>
                                </span>
                            </td>
                            <td>
                                <span class="action-badge" style="background-color: <?php echo $action_color; ?>">
                                    <?php echo htmlspecialchars($log['action']); ?>
                                </span>
                            </td>
                            <td>
                                <i class="<?php echo $target_icon; ?> target-icon"></i>
                                <?php echo htmlspecialchars($log['target_type']); ?>
                            </td>
                            <td>
                                <?php
                                // Enhanced description formatting for better readability
                                $enhanced_description = $clean_description;

                                // Add target name if available
                                if (!empty($log['target_name']) && $log['target_name'] !== '-') {
                                    $enhanced_description = $clean_description . "<br><strong>Target:</strong> " . htmlspecialchars($log['target_name']);
                                }

                                // Format details if they exist (separated by |)
                                if (strpos($enhanced_description, ' | ') !== false) {
                                    $parts = explode(' | ', $enhanced_description);
                                    $main_description = array_shift($parts);

                                    // Format each detail on a new line
                                    $formatted_details = [];
                                    foreach ($parts as $detail) {
                                        $formatted_details[] = "• " . trim($detail);
                                    }

                                    $enhanced_description = $main_description . "<br><small style='color: #6c757d; line-height: 1.4;'>" .
                                                          implode("<br>", $formatted_details) . "</small>";
                                }

                                // Replace any remaining | with line breaks for better formatting
                                $enhanced_description = str_replace(' | ', '<br>• ', $enhanced_description);

                                echo $enhanced_description;
                                ?>
                            </td>
                        </tr>
                    <?php endwhile; ?>
                </tbody>
            </table>
            
            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
                <div class="pagination">
                    <?php if ($page > 1): ?>
                        <a href="?page=1<?php echo !empty($_GET) ? '&' . http_build_query(array_diff_key($_GET, ['page' => ''])) : ''; ?>">
                            <i class="fas fa-angle-double-left"></i> Pertama
                        </a>
                        <a href="?page=<?php echo $page - 1; ?><?php echo !empty($_GET) ? '&' . http_build_query(array_diff_key($_GET, ['page' => ''])) : ''; ?>">
                            <i class="fas fa-angle-left"></i> Sebelum
                        </a>
                    <?php endif; ?>
                    
                    <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                        <?php if ($i == $page): ?>
                            <span class="current"><?php echo $i; ?></span>
                        <?php else: ?>
                            <a href="?page=<?php echo $i; ?><?php echo !empty($_GET) ? '&' . http_build_query(array_diff_key($_GET, ['page' => ''])) : ''; ?>">
                                <?php echo $i; ?>
                            </a>
                        <?php endif; ?>
                    <?php endfor; ?>
                    
                    <?php if ($page < $total_pages): ?>
                        <a href="?page=<?php echo $page + 1; ?><?php echo !empty($_GET) ? '&' . http_build_query(array_diff_key($_GET, ['page' => ''])) : ''; ?>">
                            Seterusnya <i class="fas fa-angle-right"></i>
                        </a>
                        <a href="?page=<?php echo $total_pages; ?><?php echo !empty($_GET) ? '&' . http_build_query(array_diff_key($_GET, ['page' => ''])) : ''; ?>">
                            Terakhir <i class="fas fa-angle-double-right"></i>
                        </a>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        <?php else: ?>
            <div style="text-align: center; padding: 40px; color: #6c757d;">
                <i class="fas fa-inbox" style="font-size: 48px; margin-bottom: 15px;"></i>
                <p>Tiada log aktiviti dijumpai.</p>
            </div>
        <?php endif; ?>
    </div>
</div>

</body>
</html>
