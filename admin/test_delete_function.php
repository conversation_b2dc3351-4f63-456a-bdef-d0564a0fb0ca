<?php
session_start();

if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'Admin') {
    header("Location: ../login.php");
    exit;
}

require '../db.php';

$message = "";

// Handle test delete
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_delete'])) {
    $teacher_id = (int)$_POST['teacher_id'];
    $subject_id = (int)$_POST['subject_id'];
    $classroom_id = (int)$_POST['classroom_id'];
    
    if ($teacher_id > 0 && $subject_id > 0 && $classroom_id > 0) {
        // First check if record exists
        $check = $conn->prepare("SELECT * FROM teacher_subject_classrooms WHERE teacher_id = ? AND subject_id = ? AND classroom_id = ?");
        $check->bind_param("iii", $teacher_id, $subject_id, $classroom_id);
        $check->execute();
        $result = $check->get_result();
        
        if ($result->num_rows > 0) {
            // Record exists, try to delete
            $delete = $conn->prepare("DELETE FROM teacher_subject_classrooms WHERE teacher_id = ? AND subject_id = ? AND classroom_id = ?");
            $delete->bind_param("iii", $teacher_id, $subject_id, $classroom_id);
            
            if ($delete->execute()) {
                if ($delete->affected_rows > 0) {
                    $message = "✅ Test delete successful! Record deleted.";
                } else {
                    $message = "⚠️ Delete query executed but no rows affected.";
                }
            } else {
                $message = "❌ Delete query failed: " . $delete->error;
            }
            $delete->close();
        } else {
            $message = "⚠️ No record found with the specified IDs.";
        }
        $check->close();
    } else {
        $message = "❌ Invalid IDs provided.";
    }
}

?>
<!DOCTYPE html>
<html lang="ms">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Delete Function - SMKTMI</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .section { margin: 30px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .btn { padding: 10px 20px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-danger { background-color: #dc3545; color: white; }
        table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .alert { padding: 15px; margin: 20px 0; border-radius: 4px; }
        .alert-success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .alert-danger { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .alert-warning { background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
    </style>
</head>
<body>

<div class="container">
    <h2>🧪 Test Delete Function</h2>
    
    <?php if ($message): ?>
        <div class="alert <?= strpos($message, '✅') !== false ? 'alert-success' : (strpos($message, '⚠️') !== false ? 'alert-warning' : 'alert-danger') ?>">
            <?= $message ?>
        </div>
    <?php endif; ?>
    
    <div class="section">
        <h3>Current Teacher-Subject-Classroom Assignments</h3>
        <?php
        $assignments = $conn->query("
            SELECT tsc.*, t.full_name, s.subject_name, c.class_name
            FROM teacher_subject_classrooms tsc
            JOIN teachers t ON tsc.teacher_id = t.teacher_id
            JOIN subjects s ON tsc.subject_id = s.subject_id
            JOIN classrooms c ON tsc.classroom_id = c.classroom_id
            ORDER BY t.full_name, s.subject_name, c.class_name
            LIMIT 10
        ");
        
        if ($assignments && $assignments->num_rows > 0) {
            echo "<table>";
            echo "<tr><th>Teacher ID</th><th>Teacher Name</th><th>Subject ID</th><th>Subject</th><th>Classroom ID</th><th>Class</th><th>Action</th></tr>";
            while ($row = $assignments->fetch_assoc()) {
                echo "<tr>";
                echo "<td>{$row['teacher_id']}</td>";
                echo "<td>{$row['full_name']}</td>";
                echo "<td>{$row['subject_id']}</td>";
                echo "<td>{$row['subject_name']}</td>";
                echo "<td>{$row['classroom_id']}</td>";
                echo "<td>{$row['class_name']}</td>";
                echo "<td>";
                echo "<form method='POST' style='display: inline;'>";
                echo "<input type='hidden' name='teacher_id' value='{$row['teacher_id']}'>";
                echo "<input type='hidden' name='subject_id' value='{$row['subject_id']}'>";
                echo "<input type='hidden' name='classroom_id' value='{$row['classroom_id']}'>";
                echo "<button type='submit' name='test_delete' class='btn btn-danger btn-sm' onclick='return confirm(\"Delete this assignment?\")'>Test Delete</button>";
                echo "</form>";
                echo "</td>";
                echo "</tr>";
            }
            echo "</table>";
            echo "<p><small>Showing first 10 assignments. Click 'Test Delete' to test the delete functionality.</small></p>";
        } else {
            echo "<p class='warning'>⚠️ No assignments found in the database.</p>";
        }
        ?>
    </div>
    
    <div class="section">
        <h3>Manual Test</h3>
        <form method="POST">
            <p>Enter IDs manually to test delete:</p>
            <label>Teacher ID: <input type="number" name="teacher_id" required></label><br><br>
            <label>Subject ID: <input type="number" name="subject_id" required></label><br><br>
            <label>Classroom ID: <input type="number" name="classroom_id" required></label><br><br>
            <button type="submit" name="test_delete" class="btn btn-danger" onclick="return confirm('Are you sure you want to test delete with these IDs?')">Test Delete</button>
        </form>
    </div>
    
    <div class="section">
        <h3>Database Table Structure</h3>
        <?php
        $structure = $conn->query("DESCRIBE teacher_subject_classrooms");
        if ($structure) {
            echo "<table>";
            echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
            while ($row = $structure->fetch_assoc()) {
                echo "<tr>";
                echo "<td><strong>{$row['Field']}</strong></td>";
                echo "<td>{$row['Type']}</td>";
                echo "<td>{$row['Null']}</td>";
                echo "<td>{$row['Key']}</td>";
                echo "<td>" . ($row['Default'] ?? 'NULL') . "</td>";
                echo "<td>{$row['Extra']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        ?>
    </div>
    
    <div class="section">
        <h3>Actions</h3>
        <a href="assign_teacher_subject_classroom.php" class="btn btn-primary">Back to Assignment Page</a>
        <a href="assign_teacher_subject_classroom.php?debug=1" class="btn btn-primary">Assignment Page (Debug Mode)</a>
    </div>
</div>

</body>
</html>
