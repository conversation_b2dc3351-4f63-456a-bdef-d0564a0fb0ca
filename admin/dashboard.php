<?php
session_start();

// Optional: Redirect if user is not logged in or not an admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'Admin') {
    header("Location: ../login.php");
    exit;
}

// Include database connection
require '../db.php';

// Fetch last_login and last_logout for the logged in admin
$userId = $_SESSION['user_id'];
$stmt = $conn->prepare("SELECT last_login, last_logout FROM admins WHERE user_id = ?");
$stmt->bind_param("i", $userId);
$stmt->execute();
$result = $stmt->get_result();
$adminData = $result->fetch_assoc();

// Set PHP timezone to Malaysia
date_default_timezone_set('Asia/Kuala_Lumpur');

// Convert last_logout from UTC (assuming DB stores UTC) to Malaysia time
$lastLogout = 'Never logged out';
if (!empty($adminData['last_logout'])) {
    $dateUtc = new DateTime($adminData['last_logout'], new DateTimeZone('UTC'));
    $dateUtc->setTimezone(new DateTimeZone('Asia/Kuala_Lumpur'));
    $lastLogout = $dateUtc->format('d M Y, H:i:s');
}

?>

<?php include 'includes/header.php'; ?>

<!-- FontAwesome CDN for icons -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

<style>
/* Sidebar Styling */
.sidebar {
    position: fixed;
    top: 0; left: 0;
    width: 300px;
    height: 100vh;
    background-color: #2c3e50;
    padding: 30px 20px;
    color: #ecf0f1;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    overflow-y: auto;
    box-shadow: 5px 0 15px rgba(0,0,0,0.4);
    border-right: 1px solid #34495e;
}

.sidebar h4 {
    font-size: 18px;
    font-weight: 700;
    text-transform: uppercase;
    margin: 30px 0 12px;
    border-bottom: 2px solid #3498db;
    padding-bottom: 6px;
    letter-spacing: 1.5px;
}

.sidebar a {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    color: #bdc3c7;
    text-decoration: none;
    font-size: 16px;
    margin-bottom: 8px;
    border-radius: 6px;
    transition: background-color 0.3s ease, color 0.3s ease;
}

.sidebar a i {
    margin-right: 12px;
    width: 18px;
    text-align: center;
}

.sidebar a:hover {
    background-color: #2980b9;
    color: #ecf0f1;
}

/* Content Styling */
.content {
    margin-left: 280px;
    padding: 40px 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background-color: #f9f9f9;
}

.dashboard-container {
    width: 100%;
    max-width: 900px;
    background-color: #fff;
    padding: 40px 50px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    text-align: center;
}

.dashboard-container h2 {
    font-size: 32px;
    font-weight: 700;
    margin-bottom: 30px;
    border-bottom: 3px solid #3498db;
    padding-bottom: 15px;
    color: #2980b9;
}

.dashboard-container .welcome-info {
    font-size: 18px;
    color: #7f8c8d;
    margin-top: 20px;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 6px;
    border-left: 4px solid #3498db;
}

/* Responsive */
@media (max-width: 768px) {
    .sidebar {
        width: 100%;
        height: auto;
        position: relative;
        box-shadow: none;
    }
    .content {
        margin-left: 0;
        padding: 20px;
    }
}
</style>

<?php include 'includes/sidebar.php'; ?>

<div class="content">
    <div class="dashboard-container">
        <h2>Selamat Datang, <?php echo htmlspecialchars($_SESSION['name'] ?? 'Admin'); ?></h2>
        <div class="welcome-info">
            <h6><i class="fas fa-clock"></i> Log Keluar Terakhir: <?php echo $lastLogout; ?></h6>
        </div>
    </div>
</div>

</body>
</html>
