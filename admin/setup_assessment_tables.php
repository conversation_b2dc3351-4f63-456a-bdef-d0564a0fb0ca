<?php
session_start();

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'Admin') {
    header("Location: ../login.php");
    exit;
}

require '../db.php';

$message = "";
$success = false;

// Function to verify assessment tables exist
function verifyAssessmentTables($conn) {
    $errors = [];

    // Check if assessment table exists
    $assessment_check = $conn->query("SHOW TABLES LIKE 'assessment'");
    if (!$assessment_check || $assessment_check->num_rows == 0) {
        $errors[] = "Assessment table does not exist. Please contact administrator to set up the database.";
    }

    // Check if assessment_result table exists
    $result_check = $conn->query("SHOW TABLES LIKE 'assessment_result'");
    if (!$result_check || $result_check->num_rows == 0) {
        $errors[] = "Assessment_result table does not exist. Please contact administrator to set up the database.";
    }

    return $errors;
}

// Check if tables already exist
function checkTablesExist($conn) {
    $assessment_exists = false;
    $result_exists = false;
    
    $result = $conn->query("SHOW TABLES LIKE 'assessment'");
    if ($result && $result->num_rows > 0) {
        $assessment_exists = true;
    }
    
    $result = $conn->query("SHOW TABLES LIKE 'assessment_result'");
    if ($result && $result->num_rows > 0) {
        $result_exists = true;
    }
    
    return ['assessment' => $assessment_exists, 'assessment_result' => $result_exists];
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['verify_tables'])) {
    $errors = verifyAssessmentTables($conn);

    if (empty($errors)) {
        $message = "✅ Assessment tables verified successfully!";
        $success = true;
    } else {
        $message = "❌ Tables missing:\n" . implode("\n", $errors);
    }
}

$tables_status = checkTablesExist($conn);
?>

<!DOCTYPE html>
<html lang="ms">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Setup Assessment Tables - SMKTMI</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .container {
            max-width: 800px;
            margin: 50px auto;
            padding: 30px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .status-card {
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .status-exists {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .status-missing {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .btn-primary {
            background-color: #3498db;
            border-color: #3498db;
        }
        .btn-primary:hover {
            background-color: #2980b9;
            border-color: #2980b9;
        }
    </style>
</head>
<body>

<div class="container">
    <h2 class="text-center mb-4">
        <i class="fas fa-database"></i> Setup Assessment Tables
    </h2>
    
    <?php if (!empty($message)): ?>
        <div class="alert <?php echo $success ? 'alert-success' : 'alert-danger'; ?>">
            <?php echo nl2br(htmlspecialchars($message)); ?>
        </div>
    <?php endif; ?>
    
    <h4>Current Table Status:</h4>
    
    <div class="status-card <?php echo $tables_status['assessment'] ? 'status-exists' : 'status-missing'; ?>">
        <h5>
            <i class="fas <?php echo $tables_status['assessment'] ? 'fa-check-circle' : 'fa-times-circle'; ?>"></i>
            Assessment Table
        </h5>
        <p>
            <?php if ($tables_status['assessment']): ?>
                ✅ Table exists and ready to use
            <?php else: ?>
                ❌ Table does not exist - needs to be created
            <?php endif; ?>
        </p>
    </div>
    
    <div class="status-card <?php echo $tables_status['assessment_result'] ? 'status-exists' : 'status-missing'; ?>">
        <h5>
            <i class="fas <?php echo $tables_status['assessment_result'] ? 'fa-check-circle' : 'fa-times-circle'; ?>"></i>
            Assessment Result Table
        </h5>
        <p>
            <?php if ($tables_status['assessment_result']): ?>
                ✅ Table exists and ready to use
            <?php else: ?>
                ❌ Table does not exist - needs to be created
            <?php endif; ?>
        </p>
    </div>
    
    <?php if (!$tables_status['assessment'] || !$tables_status['assessment_result']): ?>
        <div class="text-center mt-4">
            <div class="alert alert-warning">
                <h5><i class="fas fa-exclamation-triangle"></i> Tables Missing</h5>
                <p>Some required tables are missing. Please contact the system administrator to set up the database tables.</p>
                <form method="post" class="mt-3">
                    <button type="submit" name="verify_tables" class="btn btn-primary">
                        <i class="fas fa-sync-alt"></i> Verify Tables Again
                    </button>
                </form>
            </div>
        </div>
    <?php else: ?>
        <div class="text-center mt-4">
            <div class="alert alert-success">
                <h5><i class="fas fa-check-circle"></i> All Tables Ready!</h5>
                <p>Assessment management system is ready to use.</p>
                <a href="manage_assessment.php" class="btn btn-success">
                    <i class="fas fa-clipboard-list"></i> Go to Assessment Management
                </a>
            </div>
        </div>
    <?php endif; ?>
    
    <div class="text-center mt-4">
        <a href="dashboard.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Dashboard
        </a>
    </div>
</div>

</body>
</html>
