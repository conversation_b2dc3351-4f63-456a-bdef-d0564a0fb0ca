<?php
session_start();

if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'Admin') {
    header("Location: ../login.php");
    exit;
}

require '../db.php';

// Generate CSRF token
if (empty($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

$message = "";

// Handle delete operation
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'delete') {
    if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
        $message = "❌ Invalid CSRF token.";
    } else {
        $teacher_id = isset($_POST['teacher_id']) ? (int)$_POST['teacher_id'] : 0;
        $subject_id = isset($_POST['subject_id']) ? (int)$_POST['subject_id'] : 0;
        $classroom_id = isset($_POST['classroom_id']) ? (int)$_POST['classroom_id'] : 0;
        
        $message .= "Debug: teacher_id=$teacher_id, subject_id=$subject_id, classroom_id=$classroom_id<br>";
        
        if ($teacher_id > 0 && $subject_id > 0 && $classroom_id > 0) {
            // First check if record exists
            $check = $conn->prepare("SELECT * FROM teacher_subject_classrooms WHERE teacher_id = ? AND subject_id = ? AND classroom_id = ?");
            $check->bind_param("iii", $teacher_id, $subject_id, $classroom_id);
            $check->execute();
            $result = $check->get_result();
            
            if ($result->num_rows > 0) {
                $message .= "Record found, attempting delete...<br>";
                
                // Delete the record
                $delete = $conn->prepare("DELETE FROM teacher_subject_classrooms WHERE teacher_id = ? AND subject_id = ? AND classroom_id = ?");
                $delete->bind_param("iii", $teacher_id, $subject_id, $classroom_id);
                
                if ($delete->execute()) {
                    if ($delete->affected_rows > 0) {
                        $message .= "✅ Delete successful! Affected rows: " . $delete->affected_rows;
                    } else {
                        $message .= "⚠️ Delete executed but no rows affected.";
                    }
                } else {
                    $message .= "❌ Delete failed: " . $delete->error;
                }
                $delete->close();
            } else {
                $message .= "⚠️ No record found with those IDs.";
            }
            $check->close();
        } else {
            $message .= "❌ Invalid IDs provided.";
        }
    }
}

// Get some test data
$assignments = $conn->query("
    SELECT tsc.*, t.full_name, s.subject_name, c.class_name
    FROM teacher_subject_classrooms tsc
    JOIN teachers t ON tsc.teacher_id = t.teacher_id
    JOIN subjects s ON tsc.subject_id = s.subject_id
    JOIN classrooms c ON tsc.classroom_id = c.classroom_id
    ORDER BY t.full_name
    LIMIT 5
");
?>

<!DOCTYPE html>
<html lang="ms">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Delete Test - SMKTMI</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .btn { padding: 5px 10px; margin: 2px; border: none; border-radius: 3px; cursor: pointer; }
        .btn-danger { background-color: #dc3545; color: white; }
        .message { padding: 15px; margin: 20px 0; border-radius: 4px; background-color: #f8f9fa; border: 1px solid #dee2e6; }
    </style>
</head>
<body>

<div class="container">
    <h2>🧪 Simple Delete Test</h2>
    
    <?php if ($message): ?>
        <div class="message"><?= $message ?></div>
    <?php endif; ?>
    
    <h3>Test Assignments (First 5)</h3>
    <?php if ($assignments && $assignments->num_rows > 0): ?>
        <table>
            <tr>
                <th>Teacher ID</th>
                <th>Teacher</th>
                <th>Subject ID</th>
                <th>Subject</th>
                <th>Classroom ID</th>
                <th>Class</th>
                <th>Action</th>
            </tr>
            <?php while ($row = $assignments->fetch_assoc()): ?>
                <tr>
                    <td><?= $row['teacher_id'] ?></td>
                    <td><?= htmlspecialchars($row['full_name']) ?></td>
                    <td><?= $row['subject_id'] ?></td>
                    <td><?= htmlspecialchars($row['subject_name']) ?></td>
                    <td><?= $row['classroom_id'] ?></td>
                    <td><?= htmlspecialchars($row['class_name']) ?></td>
                    <td>
                        <button type="button" class="btn btn-danger" 
                                onclick="testDelete(<?= $row['teacher_id'] ?>, <?= $row['subject_id'] ?>, <?= $row['classroom_id'] ?>, '<?= htmlspecialchars($row['full_name']) ?>', '<?= htmlspecialchars($row['class_name']) ?>', '<?= htmlspecialchars($row['subject_name']) ?>')">
                            Delete
                        </button>
                    </td>
                </tr>
            <?php endwhile; ?>
        </table>
    <?php else: ?>
        <p>No assignments found.</p>
    <?php endif; ?>
    
    <h3>Manual Test</h3>
    <form method="POST">
        <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($_SESSION['csrf_token']) ?>">
        <input type="hidden" name="action" value="delete">
        
        <label>Teacher ID: <input type="number" name="teacher_id" required></label><br><br>
        <label>Subject ID: <input type="number" name="subject_id" required></label><br><br>
        <label>Classroom ID: <input type="number" name="classroom_id" required></label><br><br>
        
        <button type="submit" class="btn btn-danger">Manual Delete Test</button>
    </form>
    
    <p><a href="assign_teacher_subject_classroom.php">← Back to Assignment Page</a></p>
</div>

<!-- Hidden form for JavaScript delete -->
<form id="deleteForm" method="POST" style="display: none;">
    <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($_SESSION['csrf_token']) ?>">
    <input type="hidden" name="action" value="delete">
    <input type="hidden" name="teacher_id" id="delete_teacher_id">
    <input type="hidden" name="subject_id" id="delete_subject_id">
    <input type="hidden" name="classroom_id" id="delete_classroom_id">
</form>

<script>
function testDelete(teacherId, subjectId, classroomId, teacherName, className, subjectName) {
    console.log('testDelete called with:', {teacherId, subjectId, classroomId, teacherName, className, subjectName});
    
    const confirmMessage = `Delete assignment?\n\nTeacher: ${teacherName}\nClass: ${className}\nSubject: ${subjectName}`;
    
    if (confirm(confirmMessage)) {
        console.log('User confirmed, setting form values...');
        
        document.getElementById('delete_teacher_id').value = teacherId;
        document.getElementById('delete_subject_id').value = subjectId;
        document.getElementById('delete_classroom_id').value = classroomId;
        
        console.log('Form values set, submitting...');
        document.getElementById('deleteForm').submit();
    }
}
</script>

</body>
</html>
