<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'Admin') {
    header("Location: ../login.php");
    exit;
}

// Include database connection
require '../db.php';
require '../includes/universal_logger.php';

$id = isset($_GET['id']) ? intval($_GET['id']) : 0;
if ($id <= 0) {
    die("Invalid teacher ID.");
}

$errors = [];
$success = "";

// Fetch existing teacher data
$stmt = $conn->prepare("SELECT teacher_id, full_name, staff_id, phone_number, gender FROM teachers WHERE teacher_id = ?");
$stmt->bind_param("i", $id);
$stmt->execute();
$result = $stmt->get_result();
if ($result->num_rows != 1) {
    die("Teacher not found.");
}
$teacher = $result->fetch_assoc();
$stmt->close();

// Handle form submission
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Get input and sanitize
    $full_name = trim($_POST['full_name']);
    $staff_id = trim($_POST['staff_id']);
    $phone_number = trim($_POST['phone_number']);
    $gender = $_POST['gender'] ?? '';

    // Validate inputs
    if (empty($full_name)) $errors[] = "Nama penuh diperlukan.";
    if (empty($staff_id)) $errors[] = "ID Staf diperlukan.";
    if (!in_array($gender, ['Lelaki', 'Perempuan'])) $errors[] = "Sila pilih jantina yang sah.";

    // Check if staff_id is unique (excluding current teacher)
    if (!empty($staff_id)) {
        $check_stmt = $conn->prepare("SELECT teacher_id FROM teachers WHERE staff_id = ? AND teacher_id != ?");
        $check_stmt->bind_param("si", $staff_id, $id);
        $check_stmt->execute();
        $check_result = $check_stmt->get_result();
        if ($check_result->num_rows > 0) {
            $errors[] = "ID Staf sudah digunakan oleh guru lain.";
        }
        $check_stmt->close();
    }

    if (empty($errors)) {
        // Update DB
        $stmt = $conn->prepare("UPDATE teachers SET full_name=?, staff_id=?, phone_number=?, gender=? WHERE teacher_id=?");
        $stmt->bind_param("ssssi", $full_name, $staff_id, $phone_number, $gender, $id);
        if ($stmt->execute()) {
            // Log the teacher update
            logAdminActivity($conn, 'UPDATE', 'TEACHER', $id, $full_name,
                "Maklumat guru dikemaskini: $full_name (ID: $staff_id)");

            $success = "Maklumat guru berjaya dikemaskini.";
            // Refresh $teacher variable with new data for the form
            $teacher = [
                'teacher_id' => $id,
                'full_name' => $full_name,
                'staff_id' => $staff_id,
                'phone_number' => $phone_number,
                'gender' => $gender
            ];
        } else {
            $errors[] = "Gagal kemaskini guru: " . $stmt->error;
        }
        $stmt->close();
    }
}

include 'includes/header.php';
include 'includes/sidebar.php';
?>

<style>
    .content {
        margin-left: 280px;
        padding: 40px 30px;
        min-height: 100vh;
        background-color: #f9f9f9;
        display: flex;
        justify-content: center;
        align-items: flex-start;
        padding-top: 60px;
    }

    .form-container {
        width: 100%;
        max-width: 700px;
        background-color: #ffffff;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        border-radius: 8px;
        padding: 40px;
        color: #34495e;
    }
    .content h2 {
        font-size: 24px;
        font-weight: 700;
        margin-bottom: 20px;
        border-bottom: 2px solid #2980b9;
        padding-bottom: 10px;
        color: rgb(26, 132, 237);
    }
    .form-group {
        margin-bottom: 15px;
    }
    .form-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: 600;
        color: #2c3e50;
    }
    .form-control {
        width: 100%;
        padding: 8px 12px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
    }
    .form-control:focus {
        border-color: #3498db;
        outline: none;
        box-shadow: 0 0 5px rgba(52, 152, 219, 0.3);
    }
    .error {
        color: #e74c3c;
        background-color: #fdf2f2;
        padding: 10px;
        border-radius: 4px;
        margin-bottom: 15px;
    }
    .success {
        color: #27ae60;
        background-color: #f2fdf2;
        padding: 10px;
        border-radius: 4px;
        margin-bottom: 15px;
    }
    .gender-options {
        display: flex;
        gap: 20px;
        margin-top: 5px;
    }
    .gender-radio {
        display: flex;
        align-items: center;
        gap: 5px;
    }
    .btn {
        padding: 10px 20px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        text-decoration: none;
        display: inline-block;
        margin-right: 10px;
        font-size: 14px;
    }
    .btn-primary {
        background-color: #3498db;
        color: white;
    }
    .btn-primary:hover {
        background-color: #2980b9;
    }
    .btn-secondary {
        background-color: #95a5a6;
        color: white;
    }
    .btn-secondary:hover {
        background-color: #7f8c8d;
        text-decoration: none;
        color: white;
    }
</style>

<div class="content" role="main">
    <div class="form-container">
        <h2>Kemaskini Maklumat Guru</h2>

        <?php
        if ($success) {
            echo "<div class='success'>{$success}</div>";
        }
        if ($errors) {
            echo "<div class='error'>";
            echo "<ul style='margin: 0; padding-left: 20px;'>";
            foreach ($errors as $err) {
                echo "<li>$err</li>";
            }
            echo "</ul>";
            echo "</div>";
        }
        ?>

        <form method="post" action="">
    <div class="form-group">
        <label>Nama Penuh:</label>
        <input type="text" name="full_name" class="form-control" value="<?= htmlspecialchars($teacher['full_name']) ?>" required />
    </div>

    <div class="form-group">
        <label>ID Staf:</label>
        <input type="text" name="staff_id" class="form-control" value="<?= htmlspecialchars($teacher['staff_id']) ?>" required />
    </div>

    <div class="form-group">
        <label>Nombor Telefon:</label>
        <input type="text" name="phone_number" class="form-control" value="<?= htmlspecialchars($teacher['phone_number'] ?? '') ?>" />
    </div>

    <div class="form-group">
        <label>Jantina:</label>
        <div class="gender-options">
            <label class="gender-radio">
                <input type="radio" name="gender" value="Lelaki" <?= $teacher['gender']=='Lelaki'?'checked':'' ?> />
                <span>Lelaki</span>
            </label>
            <label class="gender-radio">
                <input type="radio" name="gender" value="Perempuan" <?= $teacher['gender']=='Perempuan'?'checked':'' ?> />
                <span>Perempuan</span>
            </label>
        </div>
    </div>

        <div style="margin-top: 25px;">
            <button type="submit" class="btn btn-primary">Kemaskini Guru</button>
            <a href="list_teachers.php" class="btn btn-secondary">Kembali ke Senarai</a>
        </div>
        </form>
    </div>
</div>

</body>
</html>
