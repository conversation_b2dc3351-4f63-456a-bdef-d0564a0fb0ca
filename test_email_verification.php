<?php
/**
 * Test script for email verification system
 * This script helps debug issues with the email verification process
 */

require 'db.php';
require 'send_email.php';

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Email Verification System Test</h2>";

// Test 1: Database Connection
echo "<h3>1. Testing Database Connection</h3>";
if ($conn->connect_error) {
    echo "❌ Database connection failed: " . $conn->connect_error . "<br>";
} else {
    echo "✅ Database connection successful<br>";
}

// Test 2: Check table structure
echo "<h3>2. Checking Table Structure</h3>";

// Check users table
$result = $conn->query("DESCRIBE users");
if ($result) {
    echo "✅ Users table exists<br>";
    echo "<details><summary>Users table structure</summary>";
    echo "<table border='1'><tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr><td>{$row['Field']}</td><td>{$row['Type']}</td><td>{$row['Null']}</td><td>{$row['Key']}</td><td>{$row['Default']}</td></tr>";
    }
    echo "</table></details>";
} else {
    echo "❌ Users table not found<br>";
}

// Check parents table
$result = $conn->query("DESCRIBE parents");
if ($result) {
    echo "✅ Parents table exists<br>";
    echo "<details><summary>Parents table structure</summary>";
    echo "<table border='1'><tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr><td>{$row['Field']}</td><td>{$row['Type']}</td><td>{$row['Null']}</td><td>{$row['Key']}</td><td>{$row['Default']}</td></tr>";
    }
    echo "</table></details>";
} else {
    echo "❌ Parents table not found<br>";
}

// Test 3: Check for unverified accounts
echo "<h3>3. Checking for Unverified Accounts</h3>";
$stmt = $conn->prepare("SELECT p.parent_id, p.full_name, u.email, p.verification_token, p.token_created_at, p.email_verified 
                        FROM parents p 
                        JOIN users u ON p.user_id = u.user_id 
                        WHERE p.email_verified = 0 
                        ORDER BY p.token_created_at DESC 
                        LIMIT 10");
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    echo "Found " . $result->num_rows . " unverified accounts:<br>";
    echo "<table border='1'><tr><th>Parent ID</th><th>Name</th><th>Email</th><th>Token</th><th>Created At</th></tr>";
    while ($row = $result->fetch_assoc()) {
        $token_display = $row['verification_token'] ? substr($row['verification_token'], 0, 8) . '...' : 'NULL';
        echo "<tr><td>{$row['parent_id']}</td><td>{$row['full_name']}</td><td>{$row['email']}</td><td>{$token_display}</td><td>{$row['token_created_at']}</td></tr>";
    }
    echo "</table>";
} else {
    echo "✅ No unverified accounts found<br>";
}

// Test 4: Test email sending (if requested)
if (isset($_GET['test_email']) && $_GET['test_email'] === '1') {
    echo "<h3>4. Testing Email Sending</h3>";
    
    $test_email = isset($_GET['email']) ? $_GET['email'] : '<EMAIL>';
    $test_token = bin2hex(random_bytes(16));
    $verify_link = "http://151.106.112.162/smktmi/verify.php?token=$test_token";
    
    echo "Sending test email to: $test_email<br>";
    echo "Test verification link: $verify_link<br>";
    
    $result = sendVerificationEmail($test_email, $verify_link);
    
    if ($result['success']) {
        echo "✅ Test email sent successfully: " . $result['message'] . "<br>";
    } else {
        echo "❌ Test email failed: " . $result['message'] . "<br>";
    }
}

// Test 5: Check PHP configuration
echo "<h3>5. PHP Configuration Check</h3>";
echo "PHP Version: " . phpversion() . "<br>";
echo "OpenSSL Extension: " . (extension_loaded('openssl') ? '✅ Loaded' : '❌ Not loaded') . "<br>";
echo "cURL Extension: " . (extension_loaded('curl') ? '✅ Loaded' : '❌ Not loaded') . "<br>";
echo "MySQLi Extension: " . (extension_loaded('mysqli') ? '✅ Loaded' : '❌ Not loaded') . "<br>";

// Test 6: Check file permissions
echo "<h3>6. File Permissions Check</h3>";
$files_to_check = ['register_process.php', 'verify.php', 'send_email.php', 'db.php'];
foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        $perms = fileperms($file);
        echo "$file: " . (is_readable($file) ? '✅ Readable' : '❌ Not readable') . 
             " | " . (is_writable($file) ? '✅ Writable' : '❌ Not writable') . 
             " | Permissions: " . substr(sprintf('%o', $perms), -4) . "<br>";
    } else {
        echo "❌ $file not found<br>";
    }
}

echo "<hr>";
echo "<h3>Test Actions</h3>";
echo "<a href='?test_email=1&email=<EMAIL>'>Test Email Sending</a><br>";
echo "<small>Replace '<EMAIL>' with a real email address to test</small><br>";

?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { border-collapse: collapse; margin: 10px 0; }
th, td { padding: 8px; text-align: left; }
details { margin: 10px 0; }
summary { cursor: pointer; font-weight: bold; }
</style>
