{"name": "phpmailer/phpmailer", "type": "library", "description": "PHPMailer is a full-featured email creation and transfer class for PHP", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "funding": [{"url": "https://github.com/Synchro", "type": "github"}], "config": {"allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true}, "lock": false}, "require": {"php": ">=5.5.0", "ext-ctype": "*", "ext-filter": "*", "ext-hash": "*"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^1.0", "doctrine/annotations": "^1.2.6 || ^1.13.3", "php-parallel-lint/php-console-highlighter": "^1.0.0", "php-parallel-lint/php-parallel-lint": "^1.3.2", "phpcompatibility/php-compatibility": "^9.3.5", "roave/security-advisories": "dev-latest", "squizlabs/php_codesniffer": "^3.7.2", "yoast/phpunit-polyfills": "^1.0.4"}, "suggest": {"decomplexity/SendOauth2": "Adapter for using XOAUTH2 authentication", "ext-mbstring": "Needed to send email in multibyte encoding charset or decode encoded addresses", "ext-openssl": "Needed for secure SMTP sending and DKIM signing", "greew/oauth2-azure-provider": "Needed for Microsoft Azure XOAUTH2 authentication", "hayageek/oauth2-yahoo": "Needed for Yahoo XOAUTH2 authentication", "league/oauth2-google": "Needed for Google XOAUTH2 authentication", "psr/log": "For optional PSR-3 debug logging", "thenetworg/oauth2-azure": "Needed for Microsoft XOAUTH2 authentication", "symfony/polyfill-mbstring": "To support UTF-8 if the Mbstring PHP extension is not enabled (^1.2)"}, "autoload": {"psr-4": {"PHPMailer\\PHPMailer\\": "src/"}}, "autoload-dev": {"psr-4": {"PHPMailer\\Test\\": "test/"}}, "license": "LGPL-2.1-only", "scripts": {"check": "./vendor/bin/phpcs", "test": "./vendor/bin/phpunit --no-coverage", "coverage": "./vendor/bin/phpunit", "lint": ["@php ./vendor/php-parallel-lint/php-parallel-lint/parallel-lint . --show-deprecated -e php,phps --exclude vendor --exclude .git --exclude build"]}}