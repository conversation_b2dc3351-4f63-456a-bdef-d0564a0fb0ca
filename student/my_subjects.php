<?php
session_start();

if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'Student') {
    header("Location: ../login.php");
    exit;
}

require '../db.php';

$userId = $_SESSION['user_id'];

// Get student ID
$stmt = $conn->prepare("SELECT student_id, full_name FROM students WHERE user_id = ?");
$stmt->bind_param("i", $userId);
$stmt->execute();
$result = $stmt->get_result();
$student = $result->fetch_assoc();

if (!$student) {
    die("Maklumat pelajar tidak dijumpai.");
}

$studentId = $student['student_id'];

// Get enrolled subjects with teacher information
$subjectsStmt = $conn->prepare("
    SELECT s.subject_name,
           t.full_name as teacher_name, t.phone_number as teacher_phone,
           c.class_name
    FROM student_subjects ss
    JOIN subjects s ON ss.subject_id = s.subject_id
    JOIN teacher_subject_classrooms tsc ON s.subject_id = tsc.subject_id
    JOIN teachers t ON tsc.teacher_id = t.teacher_id
    JOIN classrooms c ON tsc.classroom_id = c.classroom_id
    JOIN students st ON st.classroom_id = c.classroom_id
    WHERE ss.student_id = ? AND st.student_id = ?
    ORDER BY s.subject_name
");
$subjectsStmt->bind_param("ii", $studentId, $studentId);
$subjectsStmt->execute();
$subjectsResult = $subjectsStmt->get_result();

include 'includes/header.php';
?>

<!-- FontAwesome CDN for icons -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

<style>
/* Content Styling */
.content {
    margin-left: 300px;
    padding: 40px 30px;
    min-height: 100vh;
    background-color: #f9f9f9;
}

.page-container {
    max-width: 1200px;
    margin: 0 auto;
}

.page-header {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    padding: 30px;
    border-radius: 10px;
    margin-bottom: 30px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.page-header h2 {
    margin: 0;
    font-size: 28px;
    font-weight: 700;
}

.subjects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
}

.subject-card {
    background: white;
    border-radius: 10px;
    padding: 25px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border-left: 5px solid #3498db;
}

.subject-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.subject-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.subject-icon {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 20px;
}

.subject-title {
    flex: 1;
}

.subject-name {
    font-size: 18px;
    font-weight: 700;
    color: #2c3e50;
    margin: 0;
}

.subject-code {
    font-size: 14px;
    color: #7f8c8d;
    margin: 0;
}

.subject-info {
    margin-bottom: 15px;
}

.subject-description {
    color: #34495e;
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 15px;
}

.teacher-info {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 3px solid #27ae60;
}

.teacher-name {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 5px;
}

.teacher-contact {
    font-size: 14px;
    color: #7f8c8d;
}

.no-subjects {
    text-align: center;
    padding: 60px 20px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.no-subjects-icon {
    font-size: 64px;
    color: #bdc3c7;
    margin-bottom: 20px;
}

.no-subjects h3 {
    color: #2c3e50;
    margin-bottom: 10px;
}

.no-subjects p {
    color: #7f8c8d;
    font-size: 16px;
}

/* Subject color themes */
.subject-card:nth-child(4n+1) { border-left-color: #3498db; }
.subject-card:nth-child(4n+2) { border-left-color: #27ae60; }
.subject-card:nth-child(4n+3) { border-left-color: #e74c3c; }
.subject-card:nth-child(4n+4) { border-left-color: #f39c12; }

.subject-card:nth-child(4n+1) .subject-icon { background: linear-gradient(135deg, #3498db, #2980b9); }
.subject-card:nth-child(4n+2) .subject-icon { background: linear-gradient(135deg, #27ae60, #229954); }
.subject-card:nth-child(4n+3) .subject-icon { background: linear-gradient(135deg, #e74c3c, #c0392b); }
.subject-card:nth-child(4n+4) .subject-icon { background: linear-gradient(135deg, #f39c12, #e67e22); }

/* Responsive */
@media (max-width: 768px) {
    .content {
        margin-left: 0;
        padding: 20px;
    }
    .subjects-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<?php include 'includes/sidebar.php'; ?>

<div class="content">
    <div class="page-container">
        <!-- Page Header -->
        <div class="page-header">
            <h2><i class="fas fa-book"></i> Subjek Saya</h2>
            <p style="margin: 10px 0 0 0; opacity: 0.9;">Senarai subjek yang telah didaftarkan untuk <?php echo htmlspecialchars($student['full_name']); ?></p>
        </div>

        <?php if ($subjectsResult->num_rows > 0): ?>
            <div class="subjects-grid">
                <?php while ($subject = $subjectsResult->fetch_assoc()): ?>
                    <div class="subject-card">
                        <div class="subject-header">
                            <div class="subject-icon">
                                <i class="fas fa-book-open"></i>
                            </div>
                            <div class="subject-title">
                                <h3 class="subject-name"><?php echo htmlspecialchars($subject['subject_name']); ?></h3>
                                <p class="subject-code">Subjek Akademik</p>
                            </div>
                        </div>

                        <div class="teacher-info">
                            <div class="teacher-name">
                                <i class="fas fa-chalkboard-teacher"></i>
                                <?php echo htmlspecialchars($subject['teacher_name']); ?>
                            </div>
                            <div class="teacher-contact">
                                <i class="fas fa-phone"></i>
                                <?php echo htmlspecialchars($subject['teacher_phone']); ?>
                            </div>
                            <div class="teacher-contact">
                                <i class="fas fa-users"></i>
                                Kelas: <?php echo htmlspecialchars($subject['class_name']); ?>
                            </div>
                        </div>
                    </div>
                <?php endwhile; ?>
            </div>
        <?php else: ?>
            <div class="no-subjects">
                <div class="no-subjects-icon">
                    <i class="fas fa-book"></i>
                </div>
                <h3>Tiada Subjek Didaftarkan</h3>
                <p>Anda belum didaftarkan dalam mana-mana subjek. Sila hubungi guru kelas atau pihak pentadbiran untuk maklumat lanjut.</p>
            </div>
        <?php endif; ?>
    </div>
</div>

</body>
</html>
