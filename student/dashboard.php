<?php
session_start();

if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'Student') {
    header("Location: ../login.php");
    exit;
}

require '../db.php';
require '../includes/universal_logger.php';

$userId = $_SESSION['user_id'];

// Fetch student details
$stmt = $conn->prepare("SELECT s.student_id, s.full_name, s.no_ic, s.gender, s.birth_date, s.age, s.relation,
                               c.class_name, c.classroom_id,
                               p.full_name as parent_name, p.phone_number as parent_phone
                        FROM students s
                        LEFT JOIN classrooms c ON s.classroom_id = c.classroom_id
                        LEFT JOIN parents p ON s.parent_id = p.parent_id
                        WHERE s.user_id = ?");
$stmt->bind_param("i", $userId);
$stmt->execute();
$result = $stmt->get_result();
$student = $result->fetch_assoc();

if (!$student) {
    die("Maklumat pelajar tidak dijumpai.");
}

$studentId = $student['student_id'];
$classroomId = $student['classroom_id'];

// Get subjects enrolled
$subjectCount = 0;
if ($classroomId) {
    $subjectStmt = $conn->prepare("
        SELECT COUNT(*) as total
        FROM student_subjects ss
        JOIN subjects s ON ss.subject_id = s.subject_id
        WHERE ss.student_id = ?
    ");
    $subjectStmt->bind_param("i", $studentId);
    $subjectStmt->execute();
    $subjectResult = $subjectStmt->get_result();
    $subjectData = $subjectResult->fetch_assoc();
    $subjectCount = $subjectData['total'];
}

// Get attendance statistics for current month
$currentMonth = date('Y-m');
$attendanceStats = ['hadir' => 0, 'tidak_hadir' => 0, 'total' => 0];

if ($classroomId) {
    $attendanceStmt = $conn->prepare("
        SELECT status, COUNT(*) as count
        FROM attendance
        WHERE student_id = ? AND DATE_FORMAT(attendance_date, '%Y-%m') = ?
        GROUP BY status
    ");
    $attendanceStmt->bind_param("is", $studentId, $currentMonth);
    $attendanceStmt->execute();
    $attendanceResult = $attendanceStmt->get_result();

    while ($row = $attendanceResult->fetch_assoc()) {
        if ($row['status'] === 'Hadir') {
            $attendanceStats['hadir'] = $row['count'];
        } elseif ($row['status'] === 'Tidak Hadir') {
            $attendanceStats['tidak_hadir'] = $row['count'];
        }
    }
    $attendanceStats['total'] = $attendanceStats['hadir'] + $attendanceStats['tidak_hadir'];
}

// Calculate attendance percentage
$attendancePercentage = 0;
if ($attendanceStats['total'] > 0) {
    $attendancePercentage = round(($attendanceStats['hadir'] / $attendanceStats['total']) * 100, 1);
}

// Get class teacher info
$classTeacher = null;
if ($classroomId) {
    $teacherStmt = $conn->prepare("
        SELECT t.full_name, t.phone_number
        FROM teachers t
        JOIN classrooms c ON t.teacher_id = c.teacher_id
        WHERE c.classroom_id = ?
    ");
    $teacherStmt->bind_param("i", $classroomId);
    $teacherStmt->execute();
    $teacherResult = $teacherStmt->get_result();
    $classTeacher = $teacherResult->fetch_assoc();
}

include 'includes/header.php';
?>

<!-- FontAwesome CDN for icons -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

<style>
/* Content Styling */
.content {
    margin-left: 300px;
    padding: 40px 30px;
    min-height: 100vh;
    background-color: #f9f9f9;
}

.dashboard-container {
    max-width: 1200px;
    margin: 0 auto;
}

.welcome-header {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    padding: 30px;
    border-radius: 10px;
    margin-bottom: 30px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.welcome-header h2 {
    margin: 0 0 10px 0;
    font-size: 28px;
    font-weight: 700;
}

.welcome-info {
    font-size: 16px;
    opacity: 0.9;
}

/* Statistics cards styles removed */

.info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto;
    gap: 20px;
    margin-top: 30px;
}

.info-card {
    background: white;
    padding: 25px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
}

.info-card.personal {
    grid-column: 1;
    grid-row: 1;
}

.info-card.academic {
    grid-column: 2;
    grid-row: 1;
}

.info-card.parent {
    grid-column: 1;
    grid-row: 2;
}

.info-card.attendance {
    grid-column: 2;
    grid-row: 2;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 2px solid #3498db;
}

.info-card h4 {
    color: #2980b9;
    margin-bottom: 20px;
    font-size: 18px;
    font-weight: 700;
    border-bottom: 2px solid #3498db;
    padding-bottom: 10px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid #ecf0f1;
}

.info-item:last-child {
    border-bottom: none;
}

.info-label {
    font-weight: 600;
    color: #34495e;
}

.info-value {
    color: #7f8c8d;
}

.attendance-bar {
    width: 100%;
    height: 20px;
    background-color: #ecf0f1;
    border-radius: 10px;
    overflow: hidden;
    margin-top: 10px;
    border: 1px solid #bdc3c7;
}

.attendance-fill {
    height: 100%;
    background: linear-gradient(90deg, #27ae60, #2ecc71);
    transition: width 0.3s ease;
}

/* Color themes removed */

/* Creative responsive design */
@media (max-width: 768px) {
    .content {
        margin-left: 0;
        padding: 20px;
    }
    .info-grid {
        grid-template-columns: 1fr;
        grid-template-rows: auto;
    }

    .info-card.personal {
        grid-column: 1;
        grid-row: 1;
        order: 1;
    }

    .info-card.attendance {
        grid-column: 1;
        grid-row: 2;
        order: 2;
    }

    .info-card.academic {
        grid-column: 1;
        grid-row: 3;
        order: 3;
    }

    .info-card.parent {
        grid-column: 1;
        grid-row: 4;
        order: 4;
    }
}

@media (min-width: 769px) and (max-width: 1024px) {
    .info-grid {
        grid-template-columns: 1fr 1fr;
        gap: 15px;
    }

    .info-card.attendance {
        grid-column: 1 / -1;
        grid-row: 2;
    }
}
</style>

<?php include 'includes/sidebar.php'; ?>

<div class="content">
    <div class="dashboard-container">
        <!-- Welcome Header -->
        <div class="welcome-header">
            <h2>Selamat Datang, <?php echo htmlspecialchars($student['full_name']); ?>!</h2>
            <div class="welcome-info">
                <i class="fas fa-graduation-cap"></i> Portal Pelajar SMKTMI
                <?php if ($student['class_name']): ?>
                    | <i class="fas fa-users"></i> Kelas: <?php echo htmlspecialchars($student['class_name']); ?>
                <?php endif; ?>
            </div>
        </div>

        <!-- Statistics Cards Section Removed -->

        <!-- Information Cards -->
        <div class="info-grid">
            <!-- Personal Information -->
            <div class="info-card personal">
                <h4><i class="fas fa-user"></i> Maklumat Peribadi</h4>
                <div class="info-item">
                    <span class="info-label">Nama Penuh:</span>
                    <span class="info-value"><?php echo htmlspecialchars($student['full_name']); ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">No. Kad Pengenalan:</span>
                    <span class="info-value"><?php echo htmlspecialchars($student['no_ic']); ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">Jantina:</span>
                    <span class="info-value"><?php echo htmlspecialchars($student['gender']); ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">Tarikh Lahir:</span>
                    <span class="info-value"><?php echo date('d/m/Y', strtotime($student['birth_date'])); ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">Umur:</span>
                    <span class="info-value"><?php echo $student['age']; ?> tahun</span>
                </div>
            </div>

            <!-- Academic Information -->
            <div class="info-card academic">
                <h4><i class="fas fa-graduation-cap"></i> Maklumat Akademik</h4>
                <div class="info-item">
                    <span class="info-label">Kelas:</span>
                    <span class="info-value"><?php echo $student['class_name'] ? htmlspecialchars($student['class_name']) : 'Belum Ditetapkan'; ?></span>
                </div>
                <?php if ($classTeacher): ?>
                <div class="info-item">
                    <span class="info-label">Guru Kelas:</span>
                    <span class="info-value"><?php echo htmlspecialchars($classTeacher['full_name']); ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">Tel. Guru Kelas:</span>
                    <span class="info-value"><?php echo htmlspecialchars($classTeacher['phone_number']); ?></span>
                </div>
                <?php endif; ?>
                <div class="info-item">
                    <span class="info-label">Bilangan Subjek:</span>
                    <span class="info-value"><?php echo $subjectCount; ?> subjek</span>
                </div>
            </div>

            <!-- Parent Information -->
            <div class="info-card parent">
                <h4><i class="fas fa-home"></i> Maklumat Ibu Bapa/Penjaga</h4>
                <div class="info-item">
                    <span class="info-label">Nama:</span>
                    <span class="info-value"><?php echo htmlspecialchars($student['parent_name']); ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">Hubungan:</span>
                    <span class="info-value"><?php echo htmlspecialchars($student['relation']); ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">No. Telefon:</span>
                    <span class="info-value"><?php echo htmlspecialchars($student['parent_phone']); ?></span>
                </div>
            </div>

            <!-- Attendance Summary -->
            <div class="info-card attendance">
                <h4><i class="fas fa-chart-pie"></i> Ringkasan Kehadiran (<?php echo date('F Y'); ?>)</h4>
                <div class="info-item">
                    <span class="info-label">Hadir:</span>
                    <span class="info-value"><?php echo $attendanceStats['hadir']; ?> hari</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Tidak Hadir:</span>
                    <span class="info-value"><?php echo $attendanceStats['tidak_hadir']; ?> hari</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Jumlah Hari Sekolah:</span>
                    <span class="info-value"><?php echo $attendanceStats['total']; ?> hari</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Peratusan Kehadiran:</span>
                    <span class="info-value"><?php echo $attendancePercentage; ?>%</span>
                </div>
                <div class="attendance-bar">
                    <div class="attendance-fill" style="width: <?php echo $attendancePercentage; ?>%;"></div>
                </div>
            </div>
        </div>
    </div>
</div>

</body>
</html>
