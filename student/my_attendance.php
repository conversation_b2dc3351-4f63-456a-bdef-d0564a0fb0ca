<?php
session_start();

if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'Student') {
    header("Location: ../login.php");
    exit;
}

require '../db.php';

$userId = $_SESSION['user_id'];

// Get student ID
$stmt = $conn->prepare("SELECT student_id, full_name FROM students WHERE user_id = ?");
$stmt->bind_param("i", $userId);
$stmt->execute();
$result = $stmt->get_result();
$student = $result->fetch_assoc();

if (!$student) {
    die("Maklumat pelajar tidak dijumpai.");
}

$studentId = $student['student_id'];

// Get selected month (default to current month)
$selectedMonth = isset($_GET['month']) ? $_GET['month'] : date('Y-m');

// Get attendance records for selected month
$attendanceStmt = $conn->prepare("
    SELECT attendance_date, status
    FROM attendance
    WHERE student_id = ? AND DATE_FORMAT(attendance_date, '%Y-%m') = ?
    ORDER BY attendance_date DESC
");
$attendanceStmt->bind_param("is", $studentId, $selectedMonth);
$attendanceStmt->execute();
$attendanceResult = $attendanceStmt->get_result();

// Calculate statistics
$stats = ['hadir' => 0, 'tidak_hadir' => 0, 'total' => 0];
$attendanceData = [];

while ($row = $attendanceResult->fetch_assoc()) {
    $attendanceData[] = $row;
    if ($row['status'] === 'Hadir') {
        $stats['hadir']++;
    } elseif ($row['status'] === 'Tidak Hadir') {
        $stats['tidak_hadir']++;
    }
    $stats['total']++;
}

$attendancePercentage = $stats['total'] > 0 ? round(($stats['hadir'] / $stats['total']) * 100, 1) : 0;

include 'includes/header.php';
?>

<!-- FontAwesome CDN for icons -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

<style>
/* Content Styling */
.content {
    margin-left: 300px;
    padding: 40px 30px;
    min-height: 100vh;
    background-color: #f9f9f9;
}

.page-container {
    max-width: 1200px;
    margin: 0 auto;
}

.page-header {
    background: linear-gradient(135deg, #27ae60, #229954);
    color: white;
    padding: 30px;
    border-radius: 10px;
    margin-bottom: 30px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.page-header h2 {
    margin: 0;
    font-size: 28px;
    font-weight: 700;
}

.filter-section {
    background: white;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.filter-form {
    display: flex;
    align-items: center;
    gap: 15px;
}

.filter-form label {
    font-weight: 600;
    color: #2c3e50;
}

.filter-form select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
}

.filter-form button {
    padding: 8px 16px;
    background-color: #27ae60;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
}

.filter-form button:hover {
    background-color: #229954;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    text-align: center;
}

.stat-icon {
    font-size: 30px;
    margin-bottom: 10px;
}

.stat-number {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 5px;
}

.stat-label {
    color: #7f8c8d;
    font-size: 12px;
    text-transform: uppercase;
}

.attendance-table {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.table-header {
    background-color: #34495e;
    color: white;
    padding: 20px;
}

.table-header h4 {
    margin: 0;
    font-size: 18px;
}

.table {
    width: 100%;
    margin: 0;
}

.table th {
    background-color: #f8f9fa;
    padding: 15px;
    font-weight: 600;
    color: #2c3e50;
    border-bottom: 2px solid #dee2e6;
}

.table td {
    padding: 15px;
    border-bottom: 1px solid #dee2e6;
}

.status-badge {
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.status-hadir {
    background-color: #d4edda;
    color: #155724;
}

.status-tidak-hadir {
    background-color: #f8d7da;
    color: #721c24;
}

.no-attendance {
    text-align: center;
    padding: 60px 20px;
    color: #7f8c8d;
}

.no-attendance-icon {
    font-size: 64px;
    margin-bottom: 20px;
}

/* Color themes for stat cards */
.stat-hadir { color: #27ae60; }
.stat-tidak-hadir { color: #e74c3c; }
.stat-total { color: #3498db; }
.stat-percentage { color: #f39c12; }

/* Responsive */
@media (max-width: 768px) {
    .content {
        margin-left: 0;
        padding: 20px;
    }
    .filter-form {
        flex-direction: column;
        align-items: stretch;
    }
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}
</style>

<?php include 'includes/sidebar.php'; ?>

<div class="content">
    <div class="page-container">
        <!-- Page Header -->
        <div class="page-header">
            <h2><i class="fas fa-calendar-check"></i> Kehadiran Saya</h2>
            <p style="margin: 10px 0 0 0; opacity: 0.9;">Rekod kehadiran untuk <?php echo htmlspecialchars($student['full_name']); ?></p>
        </div>

        <!-- Filter Section -->
        <div class="filter-section">
            <form method="GET" class="filter-form">
                <label for="month">Pilih Bulan:</label>
                <select name="month" id="month">
                    <?php
                    // Generate month options for current year and previous year
                    $currentYear = date('Y');
                    $currentMonth = date('m');

                    for ($year = $currentYear; $year >= $currentYear - 1; $year--) {
                        for ($month = 12; $month >= 1; $month--) {
                            if ($year == $currentYear && $month > $currentMonth) continue;

                            $monthValue = sprintf('%04d-%02d', $year, $month);
                            $monthName = date('F Y', mktime(0, 0, 0, $month, 1, $year));
                            $selected = ($monthValue == $selectedMonth) ? 'selected' : '';

                            echo "<option value='$monthValue' $selected>$monthName</option>";
                        }
                    }
                    ?>
                </select>
                <button type="submit">Papar</button>
            </form>
        </div>

        <!-- Statistics Cards -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon stat-hadir">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-number"><?php echo $stats['hadir']; ?></div>
                <div class="stat-label">Hari Hadir</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon stat-tidak-hadir">
                    <i class="fas fa-times-circle"></i>
                </div>
                <div class="stat-number"><?php echo $stats['tidak_hadir']; ?></div>
                <div class="stat-label">Hari Tidak Hadir</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon stat-total">
                    <i class="fas fa-calendar-alt"></i>
                </div>
                <div class="stat-number"><?php echo $stats['total']; ?></div>
                <div class="stat-label">Jumlah Hari</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon stat-percentage">
                    <i class="fas fa-percentage"></i>
                </div>
                <div class="stat-number"><?php echo $attendancePercentage; ?>%</div>
                <div class="stat-label">Peratusan Kehadiran</div>
            </div>
        </div>

        <!-- Attendance Table -->
        <div class="attendance-table">
            <div class="table-header">
                <h4>Rekod Kehadiran - <?php echo date('F Y', strtotime($selectedMonth . '-01')); ?></h4>
            </div>

            <?php if (!empty($attendanceData)): ?>
                <table class="table">
                    <thead>
                        <tr>
                            <th>Tarikh</th>
                            <th>Hari</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($attendanceData as $record): ?>
                            <tr>
                                <td><?php echo date('d/m/Y', strtotime($record['attendance_date'])); ?></td>
                                <td><?php echo date('l', strtotime($record['attendance_date'])); ?></td>
                                <td>
                                    <span class="status-badge <?php echo $record['status'] === 'Hadir' ? 'status-hadir' : 'status-tidak-hadir'; ?>">
                                        <?php echo htmlspecialchars($record['status']); ?>
                                    </span>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php else: ?>
                <div class="no-attendance">
                    <div class="no-attendance-icon">
                        <i class="fas fa-calendar-times"></i>
                    </div>
                    <h4>Tiada Rekod Kehadiran</h4>
                    <p>Tiada rekod kehadiran untuk bulan yang dipilih.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

</body>
</html>
